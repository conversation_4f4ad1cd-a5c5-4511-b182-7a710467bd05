# WinDivert 自动下载脚本
# 自动下载并安装 WinDivert 2.2.2

param(
    [string]$Version = "2.2.2-A",
    [string]$TargetDir = "."
)

Write-Host "WinDivert 自动下载脚本" -ForegroundColor Green
Write-Host "======================" -ForegroundColor Green
Write-Host ""

# 检查管理员权限
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "❌ 错误：需要管理员权限" -ForegroundColor Red
    Write-Host "请右键点击 PowerShell，选择'以管理员身份运行'" -ForegroundColor Yellow
    Read-Host "按 Enter 键退出"
    exit 1
}

Write-Host "✅ 管理员权限检查通过" -ForegroundColor Green
Write-Host ""

# 设置下载 URL
$downloadUrl = "https://github.com/basil00/Divert/releases/download/v$Version/WinDivert-$Version.zip"
$zipFile = Join-Path $env:TEMP "WinDivert-$Version.zip"
$extractDir = Join-Path $env:TEMP "WinDivert-$Version"

Write-Host "下载 URL: $downloadUrl" -ForegroundColor Cyan
Write-Host "目标目录: $TargetDir" -ForegroundColor Cyan
Write-Host ""

try {
    # 检查是否已存在文件
    if (Test-Path (Join-Path $TargetDir "WinDivert.dll")) {
        Write-Host "⚠️  WinDivert.dll 已存在" -ForegroundColor Yellow
        $overwrite = Read-Host "是否覆盖现有文件？(y/N)"
        if ($overwrite -ne "y" -and $overwrite -ne "Y") {
            Write-Host "取消下载" -ForegroundColor Yellow
            exit 0
        }
    }

    # 下载文件
    Write-Host "正在下载 WinDivert-$Version.zip..." -ForegroundColor Yellow
    
    # 使用 .NET WebClient 下载（兼容性更好）
    $webClient = New-Object System.Net.WebClient
    $webClient.Headers.Add("User-Agent", "PowerShell/WinDivert-Downloader")
    
    # 显示下载进度
    Register-ObjectEvent -InputObject $webClient -EventName DownloadProgressChanged -Action {
        $percent = $Event.SourceEventArgs.ProgressPercentage
        Write-Progress -Activity "下载 WinDivert" -Status "$percent% 完成" -PercentComplete $percent
    } | Out-Null
    
    $webClient.DownloadFile($downloadUrl, $zipFile)
    $webClient.Dispose()
    
    Write-Progress -Activity "下载 WinDivert" -Completed
    Write-Host "✅ 下载完成" -ForegroundColor Green

    # 解压文件
    Write-Host "正在解压文件..." -ForegroundColor Yellow
    
    if (Test-Path $extractDir) {
        Remove-Item $extractDir -Recurse -Force
    }
    
    Add-Type -AssemblyName System.IO.Compression.FileSystem
    [System.IO.Compression.ZipFile]::ExtractToDirectory($zipFile, $extractDir)
    
    Write-Host "✅ 解压完成" -ForegroundColor Green

    # 检查架构
    $is64bit = [Environment]::Is64BitOperatingSystem
    $archDir = if ($is64bit) { "x64" } else { "x86" }
    $sourceDir = Join-Path $extractDir "WinDivert-$Version\$archDir"
    
    Write-Host "检测到系统架构: $archDir" -ForegroundColor Cyan

    if (-not (Test-Path $sourceDir)) {
        Write-Host "❌ 错误：找不到 $archDir 目录" -ForegroundColor Red
        exit 1
    }

    # 复制文件
    Write-Host "正在复制文件到目标目录..." -ForegroundColor Yellow
    
    $filesToCopy = @(
        "WinDivert.dll",
        "WinDivert.lib"
    )
    
    # 复制驱动文件（两个架构都需要）
    $driverFiles = @()
    if (Test-Path (Join-Path $extractDir "WinDivert-$Version\x64\WinDivert.sys")) {
        $driverFiles += @{Source = "WinDivert-$Version\x64\WinDivert.sys"; Target = "WinDivert64.sys"}
    }
    if (Test-Path (Join-Path $extractDir "WinDivert-$Version\x86\WinDivert.sys")) {
        $driverFiles += @{Source = "WinDivert-$Version\x86\WinDivert.sys"; Target = "WinDivert32.sys"}
    }

    # 复制主要文件
    foreach ($file in $filesToCopy) {
        $sourcePath = Join-Path $sourceDir $file
        $targetPath = Join-Path $TargetDir $file
        
        if (Test-Path $sourcePath) {
            Copy-Item $sourcePath $targetPath -Force
            Write-Host "✅ 复制 $file" -ForegroundColor Green
        } else {
            Write-Host "⚠️  警告：找不到 $file" -ForegroundColor Yellow
        }
    }

    # 复制驱动文件
    foreach ($driver in $driverFiles) {
        $sourcePath = Join-Path $extractDir $driver.Source
        $targetPath = Join-Path $TargetDir $driver.Target
        
        if (Test-Path $sourcePath) {
            Copy-Item $sourcePath $targetPath -Force
            Write-Host "✅ 复制 $($driver.Target)" -ForegroundColor Green
        }
    }

    # 复制头文件（如果存在 include 目录）
    $includeDir = Join-Path $TargetDir "include"
    $sourceIncludeDir = Join-Path $extractDir "WinDivert-$Version\include"
    
    if (Test-Path $sourceIncludeDir) {
        if (-not (Test-Path $includeDir)) {
            New-Item -ItemType Directory -Path $includeDir -Force | Out-Null
        }
        Copy-Item "$sourceIncludeDir\*" $includeDir -Force
        Write-Host "✅ 复制头文件到 include 目录" -ForegroundColor Green
    }

    Write-Host ""
    Write-Host "✅ WinDivert 安装完成！" -ForegroundColor Green
    Write-Host ""

    # 验证安装
    Write-Host "验证安装..." -ForegroundColor Yellow
    
    $dllPath = Join-Path $TargetDir "WinDivert.dll"
    if (Test-Path $dllPath) {
        $dllInfo = Get-Item $dllPath
        Write-Host "WinDivert.dll 大小: $($dllInfo.Length) 字节" -ForegroundColor Cyan
        Write-Host "文件版本: $($dllInfo.VersionInfo.FileVersion)" -ForegroundColor Cyan
    }

    # 运行测试程序（如果存在）
    $testPrograms = @("test-windivert.exe", "api-test.exe", "param-test.exe")
    foreach ($testProg in $testPrograms) {
        $testPath = Join-Path $TargetDir $testProg
        if (Test-Path $testPath) {
            Write-Host ""
            Write-Host "运行测试程序: $testProg" -ForegroundColor Yellow
            & $testPath
            break
        }
    }

} catch {
    Write-Host "❌ 错误: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "详细错误信息:" -ForegroundColor Red
    Write-Host $_.Exception.ToString() -ForegroundColor Red
} finally {
    # 清理临时文件
    if (Test-Path $zipFile) {
        Remove-Item $zipFile -Force
        Write-Host "清理临时文件: $zipFile" -ForegroundColor Gray
    }
    if (Test-Path $extractDir) {
        Remove-Item $extractDir -Recurse -Force
        Write-Host "清理临时目录: $extractDir" -ForegroundColor Gray
    }
}

Write-Host ""
Write-Host "现在可以运行 goproxy.exe 了！" -ForegroundColor Green
Read-Host "按 Enter 键退出"
