@echo off
chcp 65001 >nul
title 系统信息检查

echo ========================================
echo 系统信息检查
echo ========================================
echo.

echo 1. Windows 版本信息:
systeminfo | findstr /B /C:"OS Name" /C:"OS Version" /C:"System Type"

echo.
echo 2. 处理器信息:
echo PROCESSOR_ARCHITECTURE: %PROCESSOR_ARCHITECTURE%
echo PROCESSOR_ARCHITEW6432: %PROCESSOR_ARCHITEW6432%

echo.
echo 3. 详细版本信息:
ver

echo.
echo 4. PowerShell 版本:
powershell -Command "$PSVersionTable.PSVersion"

echo.
echo 5. .NET Framework 版本:
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full\" /v Release 2>nul

echo.
echo 6. 检查 WinDivert.dll 架构:
if exist WinDivert.dll (
    powershell -Command "Get-ItemProperty WinDivert.dll | Select-Object Name, Length, LastWriteTime"
    echo.
    echo 检查 DLL 架构:
    powershell -Command "[System.Reflection.AssemblyName]::GetAssemblyName('WinDivert.dll').ProcessorArchitecture" 2>nul || echo "无法检查 DLL 架构（这是正常的，因为它不是 .NET 程序集）"
) else (
    echo WinDivert.dll 不存在
)

echo.
echo 7. 检查可执行文件:
if exist minimal-test.exe (
    echo minimal-test.exe 存在
    dir minimal-test.exe
) else (
    echo minimal-test.exe 不存在
)

echo.
pause
