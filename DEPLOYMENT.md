# Deployment Guide

## Quick Deployment

### For Your Specific Use Case

Based on your requirements, here's how to deploy GoProxy for your Tailscale/Headscale setup:

#### 1. Build the Executable

On your development machine (or download from releases):

```bash
# Clone the repository
git clone <repository-url>
cd goproxy

# Build for Windows
go build -ldflags="-s -w" -o goproxy.exe ./cmd/goproxy

# Or use the build script
./build.sh
```

#### 2. Transfer to Windows Device A

Copy the following files to your Windows machine (Device A):
- `goproxy.exe`
- `config.yaml` (optional, for custom configuration)

#### 3. Configure for Your Network

Create or edit `config.yaml` on Device A:

```yaml
# Your specific configuration
source_subnet: "***********/24"
target_ipv6: "fd7a:115c:a1e0:b1a:0:7:c0a8:601"
log_level: "info"
max_connections: 1000
connection_timeout: 300
```

#### 4. Run the Proxy

On Device A (Windows), open Command Prompt as Administrator:

```cmd
cd C:\path\to\goproxy
goproxy.exe
```

You should see:
```
╔═══════════════════════════════════════════════════════════════╗
║                          GoProxy v1.0.0                      ║
║                                                               ║
║           Transparent IPv4 to IPv6 Proxy for Windows         ║
║                                                               ║
║  Intercepts IPv4 traffic and forwards it to IPv6 addresses   ║
║  Perfect for Tailscale/Headscale networks with 4via6         ║
╚═══════════════════════════════════════════════════════════════╝

[2024-08-22 10:30:00.000] INFO: Starting GoProxy v1.0.0
[2024-08-22 10:30:00.001] INFO: Configuration loaded successfully
[2024-08-22 10:30:00.002] INFO: Starting transparent proxy
[2024-08-22 10:30:00.003] INFO: Transparent proxy started successfully
[2024-08-22 10:30:00.004] INFO: Proxy is running. Press Ctrl+C to stop.
```

#### 5. Test the Connection

From Device A, you can now access Device B's downstream devices using IPv4:

```cmd
# Ping a device in the ***********/24 subnet
ping *************

# Connect to SSH
ssh user@************

# Access web interfaces
# Open browser and go to http://************
```

## Production Deployment

### Windows Service Installation

For production use, you may want to run GoProxy as a Windows service:

#### 1. Create Service Wrapper Script

Create `goproxy-service.bat`:

```batch
@echo off
cd /d "C:\Program Files\GoProxy"
goproxy.exe -config "C:\Program Files\GoProxy\config.yaml"
```

#### 2. Install as Service

Use a service wrapper like NSSM (Non-Sucking Service Manager):

```cmd
# Download NSSM from https://nssm.cc/
nssm install GoProxy "C:\Program Files\GoProxy\goproxy-service.bat"
nssm set GoProxy Description "GoProxy Transparent IPv4 to IPv6 Proxy"
nssm set GoProxy Start SERVICE_AUTO_START
nssm start GoProxy
```

### Firewall Configuration

Ensure Windows Firewall allows the proxy:

```cmd
# Allow GoProxy through firewall
netsh advfirewall firewall add rule name="GoProxy" dir=in action=allow program="C:\Program Files\GoProxy\goproxy.exe"
netsh advfirewall firewall add rule name="GoProxy" dir=out action=allow program="C:\Program Files\GoProxy\goproxy.exe"
```

### Monitoring Setup

#### 1. Enable Metrics

Update `config.yaml`:

```yaml
enable_metrics: true
metrics_port: 8080
```

#### 2. Access Metrics

View metrics at: `http://localhost:8080/metrics`

#### 3. Log Monitoring

Monitor logs for issues:

```cmd
# Run with debug logging
goproxy.exe -log-level debug > goproxy.log 2>&1
```

## Network Architecture

### Your Specific Setup

```
Device A (Windows)           Tailscale Network              Device B (Gateway)
┌─────────────────┐         ┌─────────────────┐            ┌─────────────────┐
│                 │         │                 │            │                 │
│ GoProxy         │◄────────┤ IPv6 Tunnel     │◄───────────┤ Headscale       │
│ 192.168.1.x     │         │                 │            │ 4via6 Gateway   │
│ ↓               │         │                 │            │                 │
│ fd7a:115c:...   │         │                 │            │ fd7a:115c:...   │
└─────────────────┘         └─────────────────┘            └─────────────────┘
                                                                     │
                                                            ┌─────────────────┐
                                                            │ Downstream      │
                                                            │ Devices         │
                                                            │ ***********/24  │
                                                            └─────────────────┘
```

### Traffic Flow

1. **Application on Device A** → `*************:80`
2. **GoProxy intercepts** → Translates to IPv6
3. **Tailscale tunnel** → `fd7a:115c:a1e0:b1a:0:7:c0a8:601:80`
4. **Device B receives** → Forwards to downstream device
5. **Response path** → Reverse translation back to IPv4

## Troubleshooting Deployment

### Common Issues

#### 1. "Access Denied" Error
**Solution**: Run Command Prompt as Administrator

#### 2. No Packets Intercepted
**Check**:
- Correct subnet configuration
- Tailscale connectivity: `tailscale status`
- IPv6 connectivity: `ping -6 fd7a:115c:a1e0:b1a:0:7:c0a8:601`

#### 3. High CPU Usage
**Solution**: Tune configuration:
```yaml
buffer_size: 32768      # Reduce from 65536
max_connections: 500    # Reduce from 1000
connection_timeout: 60  # Reduce from 300
```

#### 4. Antivirus Blocking
**Solution**: Add GoProxy to antivirus exclusions

### Debug Mode

Run with debug logging to diagnose issues:

```cmd
goproxy.exe -log-level debug
```

Look for messages like:
```
[DEBUG] Translating outbound packet: ********:12345 -> *************:80 (IPv6: fd7a:115c:a1e0:b1a:0:7:c0a8:601)
[DEBUG] Translating inbound packet: fd7a:115c:a1e0:b1a:0:7:c0a8:601:80 -> ********:12345
```

## Performance Optimization

### For High Traffic

```yaml
# Increase buffer size for better throughput
buffer_size: 131072

# Increase connection limit
max_connections: 5000

# Optimize for your use case
connection_timeout: 120
```

### For Low Latency

```yaml
# Smaller buffers for lower latency
buffer_size: 16384

# Faster connection cleanup
connection_timeout: 30
```

## Security Considerations

### Network Security
- GoProxy only affects traffic to the configured subnet
- No external network exposure
- Local system access only

### System Security
- Requires Administrator privileges
- Uses kernel-level packet capture
- No persistent storage of traffic data

### Best Practices
1. Run with minimal required privileges
2. Monitor logs for unusual activity
3. Keep the application updated
4. Use configuration files instead of command-line arguments
5. Implement proper backup and recovery procedures

## Backup and Recovery

### Configuration Backup
```cmd
# Backup configuration
copy config.yaml config.yaml.backup

# Backup logs (if needed)
copy goproxy.log goproxy.log.backup
```

### Recovery Procedure
1. Stop GoProxy (Ctrl+C)
2. Restore configuration from backup
3. Restart GoProxy
4. Verify connectivity

## Updates and Maintenance

### Updating GoProxy
1. Stop the current instance
2. Replace `goproxy.exe` with new version
3. Update configuration if needed
4. Restart the service
5. Test connectivity

### Regular Maintenance
- Monitor log files for errors
- Check connection statistics
- Verify Tailscale connectivity
- Update configuration as network changes

## Support and Documentation

### Additional Resources
- [Installation Guide](docs/INSTALLATION.md)
- [Usage Guide](docs/USAGE.md)
- [Troubleshooting Guide](docs/TROUBLESHOOTING.md)
- [Architecture Overview](docs/ARCHITECTURE.md)

### Getting Help
1. Check the troubleshooting guide
2. Enable debug logging
3. Collect system information
4. Open GitHub issue with details
