# WinDivert 兼容性测试脚本
# PowerShell 版本

Write-Host "WinDivert 兼容性测试" -ForegroundColor Green
Write-Host "===================="

# 检查管理员权限
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
if (-not $isAdmin) {
    Write-Host "❌ 错误：需要管理员权限" -ForegroundColor Red
    Write-Host "请右键点击 PowerShell，选择'以管理员身份运行'" -ForegroundColor Yellow
    Read-Host "按 Enter 键退出"
    exit 1
}

Write-Host "✅ 管理员权限检查通过" -ForegroundColor Green
Write-Host ""

# 检查系统信息
Write-Host "1. 系统信息检查..." -ForegroundColor Cyan
$osInfo = Get-CimInstance -ClassName Win32_OperatingSystem
Write-Host "   操作系统: $($osInfo.Caption)"
Write-Host "   版本: $($osInfo.Version)"
Write-Host "   架构: $($osInfo.OSArchitecture)"
Write-Host "   内核版本: $([System.Environment]::OSVersion.Version)"

# 检查 WinDivert 文件
Write-Host ""
Write-Host "2. WinDivert 文件检查..." -ForegroundColor Cyan

$files = @("WinDivert.dll", "WinDivert.sys", "WinDivert64.sys", "WinDivert.lib")
foreach ($file in $files) {
    if (Test-Path $file) {
        $fileInfo = Get-ItemProperty $file
        Write-Host "   ✅ $file 存在 (大小: $($fileInfo.Length) 字节)" -ForegroundColor Green
        
        # 尝试获取文件版本
        try {
            $versionInfo = [System.Diagnostics.FileVersionInfo]::GetVersionInfo($file)
            if ($versionInfo.FileVersion) {
                Write-Host "      版本: $($versionInfo.FileVersion)" -ForegroundColor Gray
            }
            if ($versionInfo.FileDescription) {
                Write-Host "      描述: $($versionInfo.FileDescription)" -ForegroundColor Gray
            }
        } catch {
            Write-Host "      无法获取版本信息" -ForegroundColor Gray
        }
    } else {
        Write-Host "   ❌ $file 不存在" -ForegroundColor Red
    }
}

# 检查驱动签名设置
Write-Host ""
Write-Host "3. 驱动签名设置检查..." -ForegroundColor Cyan

try {
    $testSigning = bcdedit /enum | Select-String "testsigning"
    if ($testSigning) {
        Write-Host "   ✅ 测试签名设置: $testSigning" -ForegroundColor Green
    } else {
        Write-Host "   ⚠️  测试签名未启用" -ForegroundColor Yellow
        Write-Host "      建议运行: bcdedit /set testsigning on" -ForegroundColor Yellow
    }
} catch {
    Write-Host "   ❌ 无法检查测试签名设置" -ForegroundColor Red
}

# 检查安全启动
Write-Host ""
Write-Host "4. 安全启动检查..." -ForegroundColor Cyan

try {
    $secureBootEnabled = Confirm-SecureBootUEFI
    if ($secureBootEnabled) {
        Write-Host "   ⚠️  安全启动已启用" -ForegroundColor Yellow
        Write-Host "      这可能阻止 WinDivert 驱动加载" -ForegroundColor Yellow
    } else {
        Write-Host "   ✅ 安全启动未启用" -ForegroundColor Green
    }
} catch {
    Write-Host "   ℹ️  无法检查安全启动状态（可能不支持）" -ForegroundColor Gray
}

# 检查 Windows Defender
Write-Host ""
Write-Host "5. Windows Defender 检查..." -ForegroundColor Cyan

try {
    $defenderStatus = Get-MpComputerStatus
    Write-Host "   防病毒启用: $($defenderStatus.AntivirusEnabled)" -ForegroundColor Gray
    Write-Host "   实时保护: $($defenderStatus.RealTimeProtectionEnabled)" -ForegroundColor Gray
    
    if ($defenderStatus.RealTimeProtectionEnabled) {
        Write-Host "   ⚠️  实时保护已启用，可能阻止 WinDivert" -ForegroundColor Yellow
    }
} catch {
    Write-Host "   ❌ 无法检查 Windows Defender 状态" -ForegroundColor Red
}

# 检查事件日志中的错误
Write-Host ""
Write-Host "6. 检查系统事件日志..." -ForegroundColor Cyan

try {
    $recentErrors = Get-EventLog -LogName System -EntryType Error -Newest 20 | 
                   Where-Object { $_.Message -like "*WinDivert*" -or $_.Source -like "*WinDivert*" }
    
    if ($recentErrors) {
        Write-Host "   ⚠️  找到 WinDivert 相关错误:" -ForegroundColor Yellow
        foreach ($error in $recentErrors) {
            Write-Host "      $($error.TimeGenerated): $($error.Message)" -ForegroundColor Gray
        }
    } else {
        Write-Host "   ✅ 未找到 WinDivert 相关错误" -ForegroundColor Green
    }
} catch {
    Write-Host "   ❌ 无法检查事件日志" -ForegroundColor Red
}

# 建议
Write-Host ""
Write-Host "7. 建议和解决方案..." -ForegroundColor Cyan

$hasWinDivertDll = Test-Path "WinDivert.dll"
$hasWinDivertSys = Test-Path "WinDivert.sys"
$hasWinDivert64Sys = Test-Path "WinDivert64.sys"

if (-not $hasWinDivertDll) {
    Write-Host "   ❌ 缺少 WinDivert.dll" -ForegroundColor Red
    Write-Host "      请从官方下载完整的 WinDivert 包" -ForegroundColor Yellow
} elseif (-not $hasWinDivertSys -and -not $hasWinDivert64Sys) {
    Write-Host "   ❌ 缺少驱动文件 (.sys)" -ForegroundColor Red
    Write-Host "      请确保有 WinDivert.sys 或 WinDivert64.sys" -ForegroundColor Yellow
} else {
    Write-Host "   ✅ WinDivert 文件完整" -ForegroundColor Green
    
    if ($hasWinDivert64Sys -and -not $hasWinDivertSys) {
        Write-Host "   💡 建议重命名 WinDivert64.sys 为 WinDivert.sys" -ForegroundColor Cyan
        $rename = Read-Host "      是否现在重命名? (y/n)"
        if ($rename -eq "y" -or $rename -eq "Y") {
            try {
                Rename-Item "WinDivert64.sys" "WinDivert.sys"
                Write-Host "   ✅ 重命名成功" -ForegroundColor Green
            } catch {
                Write-Host "   ❌ 重命名失败: $($_.Exception.Message)" -ForegroundColor Red
            }
        }
    }
}

Write-Host ""
Write-Host "其他建议:" -ForegroundColor Cyan
Write-Host "- 尝试不同版本的 WinDivert (1.4.3, 2.2.0, 2.2.2)" -ForegroundColor Gray
Write-Host "- 确保以管理员身份运行程序" -ForegroundColor Gray
Write-Host "- 临时禁用防病毒软件进行测试" -ForegroundColor Gray
Write-Host "- 如果是虚拟机，检查虚拟化设置" -ForegroundColor Gray

Write-Host ""
Write-Host "===================="
Write-Host "检查完成" -ForegroundColor Green

Read-Host "按 Enter 键退出"
