# WinDivert 版本兼容性指南

## 问题诊断

根据测试结果，你的系统：
- ✅ Windows Server 2019 (Build 17763)
- ✅ WinDivert.dll 所有函数可用
- ❌ WinDivert 驱动未加载
- ❌ 所有 WinDivertOpen 调用失败

## 推荐的解决方案

### 1. 启用测试签名（最重要）

```cmd
# 以管理员身份运行
bcdedit /set testsigning on
shutdown /r /t 0
```

**为什么需要**: Windows Server 2019 默认阻止未签名的驱动程序。

### 2. 尝试不同版本的 WinDivert

#### A. WinDivert 1.4.3 (最兼容)

**下载**: https://github.com/basil00/Divert/releases/tag/v1.4.3
**优点**: 
- 最稳定的版本
- 与旧版Windows兼容性最好
- 签名问题较少

**文件**:
```
WinDivert-1.4.3-A/
├── x64/
│   ├── WinDivert.dll
│   ├── WinDivert.sys
│   └── WinDivert.lib
```

#### B. WinDivert 2.2.0 (推荐)

**下载**: https://github.com/basil00/Divert/releases/tag/v2.2.0
**优点**:
- 比2.2.2更稳定
- 对Windows Server支持更好

#### C. WinDivert 2.2.2 (当前版本)

**问题**: 可能与Windows Server 2019有兼容性问题

### 3. 检查系统配置

#### 禁用安全启动

1. 重启进入BIOS/UEFI设置
2. 找到"Secure Boot"选项
3. 设置为"Disabled"
4. 保存并重启

#### 检查Windows Defender

```powershell
# 临时禁用实时保护
Set-MpPreference -DisableRealtimeMonitoring $true

# 添加排除路径
Add-MpPreference -ExclusionPath "C:\path\to\goproxy"
```

### 4. 手动驱动安装

使用提供的 `install-windivert-driver.bat` 脚本：

```cmd
# 以管理员身份运行
install-windivert-driver.bat
```

### 5. 验证修复

修复后运行测试：

```cmd
# 测试API
api-test.exe

# 测试基本功能
safe-test.exe

# 测试主程序
goproxy.exe -log-level debug
```

## 预期结果

修复成功后，`api-test.exe` 应该显示：

```
5. 检查驱动状态...
   ✅ 可以访问服务控制管理器
   ✅ 找到 WinDivert 服务

3. 按照官方 API 规范测试...
   基本测试: 返回=0x[有效句柄], 错误=The operation completed successfully. ✅ 成功！
```

## 故障排除

### 如果测试签名启用后仍然失败

1. **检查事件日志**:
   ```cmd
   eventvwr.msc
   # 查看 Windows 日志 > 系统
   # 搜索 WinDivert 相关错误
   ```

2. **尝试旧版本WinDivert**:
   - 下载 WinDivert 1.4.3
   - 替换当前文件
   - 重新测试

3. **检查虚拟化环境**:
   - 如果在虚拟机中，确保启用了嵌套虚拟化
   - 检查虚拟机网络设置

### 如果所有方法都失败

考虑替代方案：
1. **使用 WinPcap/Npcap** 替代 WinDivert
2. **使用用户模式代理** 而不是内核级拦截
3. **使用路由表修改** 实现流量重定向

## 联系支持

如果问题持续存在，请提供：
1. `api-test.exe` 的完整输出
2. 事件查看器中的相关错误
3. `bcdedit /enum` 的输出
4. 使用的 WinDivert 版本信息
