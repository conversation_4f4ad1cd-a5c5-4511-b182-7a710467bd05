# Makefile for GoProxy

# Variables
APP_NAME := goproxy
VERSION := 1.0.0
BUILD_DIR := build
DIST_DIR := dist

# Go build variables
GOOS := windows
GOARCH := amd64
CGO_ENABLED := 0

# Build flags
LDFLAGS := -s -w -X main.appVersion=$(VERSION)
BUILD_FLAGS := -ldflags="$(LDFLAGS)"

# Default target
.PHONY: all
all: clean build

# Build the application
.PHONY: build
build:
	@echo "Building $(APP_NAME) v$(VERSION) for $(GOOS)/$(GOARCH)..."
	@mkdir -p $(BUILD_DIR)
	GOOS=$(GOOS) GOARCH=$(GOARCH) CGO_ENABLED=$(CGO_ENABLED) \
		go build $(BUILD_FLAGS) -o $(BUILD_DIR)/$(APP_NAME).exe ./cmd/$(APP_NAME)
	@echo "Build complete: $(BUILD_DIR)/$(APP_NAME).exe"

# Build for multiple platforms
.PHONY: build-all
build-all: clean
	@echo "Building for multiple platforms..."
	@mkdir -p $(BUILD_DIR)
	
	# Windows AMD64
	GOOS=windows GOARCH=amd64 CGO_ENABLED=0 \
		go build $(BUILD_FLAGS) -o $(BUILD_DIR)/$(APP_NAME)-windows-amd64.exe ./cmd/$(APP_NAME)
	
	# Windows ARM64
	GOOS=windows GOARCH=arm64 CGO_ENABLED=0 \
		go build $(BUILD_FLAGS) -o $(BUILD_DIR)/$(APP_NAME)-windows-arm64.exe ./cmd/$(APP_NAME)
	
	@echo "Multi-platform build complete"

# Create distribution package
.PHONY: dist
dist: build
	@echo "Creating distribution package..."
	@mkdir -p $(DIST_DIR)
	@cp $(BUILD_DIR)/$(APP_NAME).exe $(DIST_DIR)/
	@cp config.yaml $(DIST_DIR)/
	@cp README.md $(DIST_DIR)/
	@cp -r docs $(DIST_DIR)/
	@echo "Distribution package created in $(DIST_DIR)/"

# Run tests
.PHONY: test
test:
	@echo "Running tests..."
	go test -v ./...

# Run tests with coverage
.PHONY: test-coverage
test-coverage:
	@echo "Running tests with coverage..."
	go test -v -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

# Lint the code
.PHONY: lint
lint:
	@echo "Running linter..."
	golangci-lint run

# Format the code
.PHONY: fmt
fmt:
	@echo "Formatting code..."
	go fmt ./...

# Tidy dependencies
.PHONY: tidy
tidy:
	@echo "Tidying dependencies..."
	go mod tidy

# Clean build artifacts
.PHONY: clean
clean:
	@echo "Cleaning build artifacts..."
	@rm -rf $(BUILD_DIR)
	@rm -rf $(DIST_DIR)
	@rm -f coverage.out coverage.html

# Development build (with debug info)
.PHONY: dev
dev:
	@echo "Building development version..."
	@mkdir -p $(BUILD_DIR)
	GOOS=$(GOOS) GOARCH=$(GOARCH) CGO_ENABLED=$(CGO_ENABLED) \
		go build -o $(BUILD_DIR)/$(APP_NAME)-dev.exe ./cmd/$(APP_NAME)
	@echo "Development build complete: $(BUILD_DIR)/$(APP_NAME)-dev.exe"

# Install dependencies
.PHONY: deps
deps:
	@echo "Installing dependencies..."
	go mod download

# Verify the build
.PHONY: verify
verify: build
	@echo "Verifying build..."
	@if [ -f "$(BUILD_DIR)/$(APP_NAME).exe" ]; then \
		echo "✓ Executable exists"; \
		file $(BUILD_DIR)/$(APP_NAME).exe; \
	else \
		echo "✗ Executable not found"; \
		exit 1; \
	fi

# Show help
.PHONY: help
help:
	@echo "Available targets:"
	@echo "  all          - Clean and build (default)"
	@echo "  build        - Build the application for Windows"
	@echo "  build-all    - Build for multiple platforms"
	@echo "  dist         - Create distribution package"
	@echo "  test         - Run tests"
	@echo "  test-coverage- Run tests with coverage report"
	@echo "  lint         - Run linter"
	@echo "  fmt          - Format code"
	@echo "  tidy         - Tidy dependencies"
	@echo "  clean        - Clean build artifacts"
	@echo "  dev          - Build development version"
	@echo "  deps         - Install dependencies"
	@echo "  verify       - Verify the build"
	@echo "  help         - Show this help"

# Version information
.PHONY: version
version:
	@echo "$(APP_NAME) v$(VERSION)"
	@echo "Target: $(GOOS)/$(GOARCH)"
	@echo "Go version: $(shell go version)"
