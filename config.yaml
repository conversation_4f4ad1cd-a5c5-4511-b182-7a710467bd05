# GoProxy Configuration File
# This file contains configuration options for the transparent IPv4 to IPv6 proxy

# Source subnet to intercept (IPv4 CIDR notation)
# All traffic destined for this subnet will be captured and forwarded
source_subnet: "***********/24"

# Target IPv6 address where intercepted traffic will be forwarded
# This should be the IPv6 address of your Tailscale/Headscale gateway
target_ipv6: "fd7a:115c:a1e0:b1a:0:7:c0a8:601"

# Logging configuration
# Valid levels: debug, info, warn, error
log_level: "info"

# Network interface to bind to (optional)
# Leave empty to bind to all interfaces
bind_interface: ""

# Connection limits and timeouts
# Maximum number of concurrent connections (0 = unlimited)
max_connections: 1000

# Connection timeout in seconds for idle connections
connection_timeout: 300

# Buffer size for packet processing (bytes)
buffer_size: 65536

# Metrics configuration
# Enable Prometheus metrics endpoint
enable_metrics: false

# Port for metrics HTTP server
metrics_port: 8080
