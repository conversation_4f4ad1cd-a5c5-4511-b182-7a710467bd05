# GoProxy - Transparent IPv4 to IPv6 Proxy for Windows

A transparent IP proxy written in Go that intercepts IPv4 traffic destined for a specific subnet and forwards it to an IPv6 address. Designed for Tailscale/Headscale networks with 4via6 functionality.

## Overview

This proxy allows you to access IPv6-only devices using familiar IPv4 addresses. It's particularly useful for:
- Tailscale networks with 4via6 subnet routing
- Accessing downstream devices behind IPv6 gateways
- Simplifying network access without remembering complex IPv6 addresses

## Features

- **Transparent Proxying**: Intercepts traffic at the network layer
- **Protocol Agnostic**: Supports TCP, UDP, and other IP protocols
- **Configurable**: Customizable source subnet and target IPv6 address
- **Windows Native**: Uses WinDivert for efficient packet capture
- **Zero Configuration**: Works without modifying client applications

## Network Scenario

```
Device A (Windows) ──┐
                     ├── Tailscale Network ──── Device B (IPv6 Gateway)
Other Devices ───────┘                              │
                                                     └── Downstream Devices
                                                         (***********/24)
```

## Quick Start

1. Download the latest release or build from source
2. Run as Administrator (required for packet capture)
3. Configure your target subnet and IPv6 address
4. Access devices using IPv4 addresses (e.g., *************)

## Configuration

The proxy can be configured via:
- Command line flags
- Configuration file (config.yaml)
- Environment variables

### Example Configuration

```yaml
source_subnet: "***********/24"
target_ipv6: "fd7a:115c:a1e0:b1a:0:7:c0a8:601"
log_level: "info"
```

## Building

### Prerequisites
- Go 1.19 or later
- Windows 10/11 (for WinDivert support)
- Administrator privileges (for packet capture)

### Build Commands

```bash
# Build for current platform
go build -o goproxy.exe ./cmd/goproxy

# Build with optimizations
go build -ldflags="-s -w" -o goproxy.exe ./cmd/goproxy

# Cross-compile for Windows (from other platforms)
GOOS=windows GOARCH=amd64 go build -o goproxy.exe ./cmd/goproxy
```

## Usage

```bash
# Basic usage
goproxy.exe

# With custom configuration
goproxy.exe -subnet ***********/24 -target fd7a:115c:a1e0:b1a:0:7:c0a8:601

# With configuration file
goproxy.exe -config config.yaml

# Enable debug logging
goproxy.exe -log-level debug
```

## Architecture

The proxy consists of several key components:

1. **Packet Interceptor**: Captures outbound IPv4 packets using WinDivert
2. **Address Translator**: Maps IPv4 addresses to IPv6 destinations
3. **Connection Tracker**: Maintains state for active connections
4. **Packet Forwarder**: Sends translated packets to IPv6 destinations
5. **Response Handler**: Processes return traffic and translates back to IPv4

## Requirements

- Windows 10/11 (64-bit)
- Administrator privileges
- WinDivert driver (included with releases)
- Active network connection to IPv6 target

## License

MIT License - see LICENSE file for details

## Contributing

Contributions welcome! Please read CONTRIBUTING.md for guidelines.

## Support

For issues and questions:
- GitHub Issues: Report bugs and feature requests
- Documentation: See docs/ directory for detailed guides
