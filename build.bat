@echo off
REM Build script for GoProxy Windows executable

echo Building GoProxy for Windows...

REM Set build variables
set GOOS=windows
set GOARCH=amd64
set CGO_ENABLED=0

REM Build with optimizations
go build -ldflags="-s -w -X main.appVersion=%1" -o goproxy.exe ./cmd/goproxy

if %ERRORLEVEL% EQU 0 (
    echo.
    echo Build successful! 
    echo Executable: goproxy.exe
    echo.
    echo To run the proxy:
    echo   1. Run as Administrator (required for packet capture)
    echo   2. Execute: goproxy.exe
    echo.
    echo For help: goproxy.exe -h
) else (
    echo.
    echo Build failed!
    exit /b 1
)
