@echo off
chcp 65001 >nul
title GoProxy 编译脚本

echo ========================================
echo GoProxy Windows 编译脚本
echo ========================================
echo.

REM 检查 Go 是否安装
go version >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ 错误：Go 未安装或不在 PATH 中
    echo 请安装 Go 1.19 或更高版本
    pause
    exit /b 1
)

echo ✅ Go 环境检查通过
go version
echo.

REM 设置编译参数
set GOOS=windows
set GOARCH=amd64
set CGO_ENABLED=0

REM 获取版本号
set VERSION=%1
if "%VERSION%"=="" set VERSION=1.0.0

echo 编译版本: %VERSION%
echo 目标平台: %GOOS%/%GOARCH%
echo.

REM 编译主程序
echo 1. 编译主程序...
go build -ldflags="-s -w -X main.appVersion=%VERSION%" -o goproxy.exe ./cmd/goproxy
if %errorLevel% neq 0 (
    echo ❌ 主程序编译失败
    pause
    exit /b 1
)
echo ✅ goproxy.exe 编译成功

REM 编译修复版本
echo.
echo 2. 编译修复版本...
go build -ldflags="-s -w -X main.appVersion=%VERSION%" -o goproxy-fixed.exe ./cmd/goproxy
if %errorLevel% neq 0 (
    echo ❌ 修复版本编译失败
    pause
    exit /b 1
)
echo ✅ goproxy-fixed.exe 编译成功

REM 编译测试程序
echo.
echo 3. 编译测试程序...

echo   - fixed-test.exe...
go build -ldflags="-s -w" -o fixed-test.exe ./cmd/fixed-test
if %errorLevel% neq 0 (
    echo ❌ fixed-test.exe 编译失败
) else (
    echo ✅ fixed-test.exe 编译成功
)

echo   - param-test.exe...
go build -ldflags="-s -w" -o param-test.exe ./cmd/param-test
if %errorLevel% neq 0 (
    echo ❌ param-test.exe 编译失败
) else (
    echo ✅ param-test.exe 编译成功
)

echo   - api-test.exe...
go build -ldflags="-s -w" -o api-test.exe ./cmd/api-test
if %errorLevel% neq 0 (
    echo ❌ api-test.exe 编译失败
) else (
    echo ✅ api-test.exe 编译成功
)

echo.
echo ========================================
echo 编译完成
echo ========================================
echo.
echo 生成的可执行文件:
echo   - goproxy.exe (主程序)
echo   - goproxy-fixed.exe (修复版本)
echo   - fixed-test.exe (综合测试)
echo   - param-test.exe (参数测试)
echo   - api-test.exe (API测试)
echo.
echo 使用步骤:
echo   1. 下载 WinDivert: 以管理员身份运行 download-windivert.ps1
echo   2. 测试修复: 以管理员身份运行 fixed-test.exe
echo   3. 运行代理: 以管理员身份运行 goproxy-fixed.exe
echo.
echo 获取帮助: goproxy-fixed.exe -h
echo.
pause
