@echo off
chcp 65001 >nul
title WinDivert 驱动安装

echo ========================================
echo WinDivert 驱动手动安装
echo ========================================
echo.

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ 错误：需要管理员权限
    echo 请右键点击此脚本，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo ✅ 管理员权限检查通过
echo.

echo 当前目录: %CD%
echo.

REM 检查文件
if not exist WinDivert.dll (
    echo ❌ WinDivert.dll 不存在
    goto :end
)

if not exist WinDivert.sys (
    if exist WinDivert64.sys (
        echo 重命名 WinDivert64.sys 为 WinDivert.sys...
        ren WinDivert64.sys WinDivert.sys
    ) else (
        echo ❌ WinDivert.sys 不存在
        goto :end
    )
)

echo ✅ 文件检查通过
echo.

echo 1. 检查当前测试签名状态...
bcdedit /enum | findstr /i testsigning
if %errorLevel__ == 0 (
    echo ✅ 测试签名已启用
) else (
    echo ⚠️  测试签名未启用
    echo.
    choice /C YN /M "是否启用测试签名（需要重启）"
    if errorlevel 2 goto :skip_testsigning
    
    echo 启用测试签名...
    bcdedit /set testsigning on
    if %errorLevel__ == 0 (
        echo ✅ 测试签名已启用
        echo ⚠️  需要重启计算机才能生效
        echo.
        choice /C YN /M "是否现在重启"
        if not errorlevel 2 (
            shutdown /r /t 10
            echo 10秒后重启...
            pause
            exit /b 0
        )
    ) else (
        echo ❌ 启用测试签名失败
    )
)

:skip_testsigning

echo.
echo 2. 尝试手动加载驱动...

REM 停止可能存在的服务
sc stop WinDivert >nul 2>&1
sc delete WinDivert >nul 2>&1

echo 创建 WinDivert 服务...
sc create WinDivert binPath= "%CD%\WinDivert.sys" type= kernel start= demand DisplayName= "WinDivert Packet Capture Driver"

if %errorLevel__ == 0 (
    echo ✅ 服务创建成功
    
    echo 启动 WinDivert 服务...
    sc start WinDivert
    
    if %errorLevel__ == 0 (
        echo ✅ 驱动加载成功
        
        echo 检查服务状态...
        sc query WinDivert
        
        echo.
        echo 测试 WinDivert 功能...
        if exist api-test.exe (
            api-test.exe
        ) else (
            echo api-test.exe 不存在，请手动测试
        )
        
        echo.
        choice /C YN /M "是否停止并删除测试服务"
        if not errorlevel 2 (
            sc stop WinDivert
            sc delete WinDivert
            echo ✅ 测试服务已清理
        )
        
    ) else (
        echo ❌ 驱动启动失败
        echo 检查错误信息:
        sc query WinDivert
        
        echo 删除失败的服务...
        sc delete WinDivert >nul 2>&1
    )
) else (
    echo ❌ 服务创建失败
)

echo.
echo 3. 检查事件日志中的错误...
echo 查找最近的系统错误:
powershell -Command "Get-EventLog -LogName System -EntryType Error -Newest 5 | Where-Object {$_.Message -like '*WinDivert*' -or $_.Message -like '*driver*'} | Format-Table TimeGenerated, EventID, Message -Wrap"

:end
echo.
echo ========================================
echo 安装完成
echo ========================================
echo.
echo 如果驱动仍然无法加载，请尝试:
echo 1. 重启计算机（如果刚启用测试签名）
echo 2. 在 BIOS/UEFI 中禁用安全启动
echo 3. 尝试不同版本的 WinDivert
echo 4. 检查防病毒软件是否阻止
echo.
pause
