@echo off
chcp 65001 >nul
title 文件检查工具

echo ========================================
echo 文件存在性检查
echo ========================================
echo.

echo 当前目录: %CD%
echo.

echo 检查文件列表:
echo ----------------------------------------
if exist minimal-test.exe (
    echo ✅ minimal-test.exe 存在
    dir minimal-test.exe
) else (
    echo ❌ minimal-test.exe 不存在
)

if exist goproxy.exe (
    echo ✅ goproxy.exe 存在
    dir goproxy.exe
) else (
    echo ❌ goproxy.exe 不存在
)

if exist test-windivert.exe (
    echo ✅ test-windivert.exe 存在
    dir test-windivert.exe
) else (
    echo ❌ test-windivert.exe 不存在
)

if exist WinDivert.dll (
    echo ✅ WinDivert.dll 存在
    dir WinDivert.dll
) else (
    echo ❌ WinDivert.dll 不存在
)

echo.
echo ----------------------------------------
echo 所有 .exe 文件:
dir *.exe

echo.
echo ----------------------------------------
echo 系统信息:
echo Windows版本:
ver

echo.
echo 处理器架构:
echo %PROCESSOR_ARCHITECTURE%

echo.
echo 当前用户权限:
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ 具有管理员权限
) else (
    echo ❌ 没有管理员权限
)

echo.
echo ----------------------------------------
echo 尝试运行测试:

if exist minimal-test.exe (
    echo 尝试运行 minimal-test.exe...
    echo.
    minimal-test.exe
    echo.
    echo 退出代码: %ERRORLEVEL%
) else (
    echo 无法运行 minimal-test.exe - 文件不存在
)

echo.
echo 测试完成。
pause
