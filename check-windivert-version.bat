@echo off
chcp 65001 >nul
title WinDivert 版本检查

echo ========================================
echo WinDivert 版本和兼容性检查
echo ========================================
echo.

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ 错误：需要管理员权限
    pause
    exit /b 1
)

echo ✅ 管理员权限检查通过
echo.

echo 1. 检查 WinDivert.dll 详细信息...
if exist WinDivert.dll (
    echo ✅ WinDivert.dll 存在
    dir WinDivert.dll
    echo.
    
    echo 文件版本信息:
    powershell -Command "Get-ItemProperty WinDivert.dll | Format-List Name, Length, CreationTime, LastWriteTime"
    
    echo.
    echo 尝试获取文件版本:
    powershell -Command "[System.Diagnostics.FileVersionInfo]::GetVersionInfo('WinDivert.dll') | Format-List FileVersion, ProductVersion, FileDescription"
    
) else (
    echo ❌ WinDivert.dll 不存在
    goto :end
)

echo.
echo 2. 检查是否有其他 WinDivert 文件...
if exist WinDivert.sys (
    echo ✅ 找到 WinDivert.sys
    dir WinDivert.sys
) else (
    echo ⚠️  WinDivert.sys 不存在
    echo 这可能是问题所在 - WinDivert 需要 .sys 驱动文件
)

if exist WinDivert.lib (
    echo ✅ 找到 WinDivert.lib
) else (
    echo ⚠️  WinDivert.lib 不存在
)

echo.
echo 3. 检查系统兼容性...
echo 系统信息:
systeminfo | findstr /B /C:"OS Name" /C:"OS Version" /C:"System Type"

echo.
echo 4. 检查已安装的驱动...
echo 查找 WinDivert 相关驱动:
sc query | findstr /i windivert
if %errorLevel__ == 0 (
    echo ✅ 找到 WinDivert 驱动服务
) else (
    echo ⚠️  未找到 WinDivert 驱动服务
)

echo.
echo 5. 检查驱动签名设置...
bcdedit /enum | findstr /i testsigning
if %errorLevel__ == 0 (
    echo ✅ 测试签名已启用
) else (
    echo ⚠️  测试签名未启用
    echo.
    echo 建议启用测试签名以支持 WinDivert 驱动:
    echo bcdedit /set testsigning on
    echo 然后重启计算机
)

echo.
echo 6. 建议的解决方案...
echo.
echo 基于检查结果，建议尝试以下解决方案:
echo.
echo A. 确保完整的 WinDivert 文件:
echo    - WinDivert.dll (主库文件)
echo    - WinDivert.sys (驱动文件) ← 可能缺失
echo    - 从 WinDivert 官方下载完整包
echo.
echo B. 尝试不同版本:
echo    - WinDivert 2.2.0 (较稳定)
echo    - WinDivert 1.4.3 (最兼容)
echo.
echo C. 系统配置:
echo    - 启用测试签名: bcdedit /set testsigning on
echo    - 重启计算机
echo    - 确保以管理员身份运行
echo.
echo D. 检查冲突:
echo    - 关闭其他网络监控软件
echo    - 临时禁用防病毒软件
echo    - 检查是否有其他 WinDivert 实例运行

:end
echo.
echo ========================================
echo 检查完成
echo ========================================
pause
