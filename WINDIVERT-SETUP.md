# WinDivert 安装指南

## 问题说明

如果你遇到以下错误：
- "WinDivert.dll not found"
- "The parameter is incorrect"
- 程序闪退

这通常是因为缺少 WinDivert.dll 文件或版本不兼容。

## 解决方案

### 步骤 1: 下载 WinDivert

1. **访问官方网站**：https://www.reqrypt.org/windivert.html

2. **下载最新版本**：
   - 点击 "Download WinDivert-2.2.2-A.zip" (或最新版本)
   - 文件大小约 1.5MB

3. **验证下载**：
   - 确保从官方网站下载
   - 检查文件完整性

### 步骤 2: 解压文件

1. **解压 ZIP 文件**到临时目录

2. **找到正确的 DLL**：
   ```
   WinDivert-2.2.2-A/
   ├── x64/
   │   ├── WinDivert.dll     ← 64位版本 (需要这个)
   │   ├── WinDivert.lib
   │   └── WinDivert.sys
   ├── x86/
   │   ├── WinDivert.dll     ← 32位版本
   │   ├── WinDivert.lib
   │   └── WinDivert.sys
   └── include/
   ```

3. **选择正确版本**：
   - 对于 64位 Windows：使用 `x64/WinDivert.dll`
   - 对于 32位 Windows：使用 `x86/WinDivert.dll`

### 步骤 3: 复制文件

1. **复制 WinDivert.dll** 到 goproxy.exe 所在目录：
   ```
   C:\your\goproxy\directory\
   ├── goproxy.exe
   ├── WinDivert.dll        ← 复制到这里
   ├── config.yaml
   └── start-goproxy.bat
   ```

2. **验证文件**：
   - 确保 WinDivert.dll 与 goproxy.exe 在同一目录
   - 检查文件大小（约 200KB）

### 步骤 4: 测试安装

1. **运行测试程序**：
   ```cmd
   # 以管理员身份运行命令提示符
   cd C:\path\to\goproxy
   test-windivert.exe
   ```

2. **预期输出**：
   ```
   WinDivert 测试程序
   ==================
   1. 测试加载 WinDivert 库...
   ✅ WinDivert 库加载成功
   
   2. 测试简单过滤器...
      测试过滤器: true ... ✅ 成功
      测试过滤器: false ... ✅ 成功
      测试过滤器: outbound ... ✅ 成功
      ...
   ```

### 步骤 5: 运行主程序

1. **使用启动脚本**：
   ```cmd
   # 右键点击 start-goproxy.bat
   # 选择 "以管理员身份运行"
   ```

2. **或手动运行**：
   ```cmd
   # 以管理员身份运行命令提示符
   cd C:\path\to\goproxy
   goproxy.exe -log-level debug
   ```

## 常见问题

### 问题 1: "The parameter is incorrect"

**原因**：WinDivert 过滤器语法错误

**解决方案**：
1. 确保使用最新版本的 WinDivert.dll
2. 检查防病毒软件是否阻止
3. 尝试运行测试程序验证基本功能

### 问题 2: "Access denied"

**原因**：权限不足

**解决方案**：
1. 必须以管理员身份运行
2. 右键命令提示符 → "以管理员身份运行"

### 问题 3: "WinDivert.dll not found"

**原因**：DLL 文件不在正确位置

**解决方案**：
1. 确保 WinDivert.dll 与 goproxy.exe 在同一目录
2. 检查是否下载了正确的架构版本（x64 vs x86）

### 问题 4: 防病毒软件阻止

**原因**：防病毒软件将 WinDivert 识别为潜在威胁

**解决方案**：
1. 将整个 goproxy 目录添加到防病毒软件白名单
2. 临时禁用实时保护进行测试
3. 检查防病毒软件日志

### 问题 5: Windows Defender SmartScreen

**原因**：Windows 阻止未签名的程序

**解决方案**：
1. 点击 "更多信息"
2. 点击 "仍要运行"
3. 添加永久例外

## 验证安装

### 检查清单

- [ ] Windows 10/11 64位系统
- [ ] 以管理员身份运行
- [ ] WinDivert.dll 在正确位置
- [ ] 防病毒软件已添加例外
- [ ] 测试程序运行成功

### 测试命令

```cmd
# 1. 检查文件
dir WinDivert.dll
dir goproxy.exe

# 2. 测试 WinDivert
test-windivert.exe

# 3. 测试主程序
goproxy.exe -h

# 4. 运行调试模式
goproxy.exe -log-level debug
```

## 高级配置

### 自定义 WinDivert 位置

如果需要将 WinDivert.dll 放在其他位置：

1. **系统 PATH**：将 DLL 放在 System32 目录
2. **环境变量**：设置 PATH 包含 DLL 目录
3. **应用程序目录**：推荐方式，与 exe 同目录

### 性能优化

对于高流量环境：

1. **使用最新版本** WinDivert
2. **调整缓冲区大小**：
   ```yaml
   buffer_size: 131072  # 增加缓冲区
   ```
3. **优化过滤器**：使用更精确的过滤条件

## 获取帮助

如果仍有问题：

1. **运行诊断脚本**：`diagnose.bat`
2. **查看详细日志**：`goproxy.exe -log-level debug > debug.log 2>&1`
3. **检查系统事件日志**：Windows 事件查看器
4. **提供以下信息**：
   - Windows 版本
   - WinDivert 版本
   - 错误消息
   - 调试日志

## 安全说明

- WinDivert 是合法的网络工具
- 仅在本地系统上运行
- 不会发送数据到外部服务器
- 需要管理员权限是正常的
- 防病毒软件可能误报，这是常见现象
