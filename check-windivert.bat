@echo off
chcp 65001 >nul
title WinDivert 兼容性检查

echo ========================================
echo WinDivert 兼容性检查
echo ========================================
echo.

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ 错误：需要管理员权限
    echo 请右键点击此脚本，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo ✅ 管理员权限检查通过
echo.

echo 1. 检查 WinDivert.dll 文件...
if exist WinDivert.dll (
    echo ✅ WinDivert.dll 存在
    dir WinDivert.dll
    
    echo.
    echo 检查文件版本信息:
    powershell -Command "Get-ItemProperty WinDivert.dll | Select-Object Name, Length, LastWriteTime, VersionInfo"
) else (
    echo ❌ WinDivert.dll 不存在
    echo.
    echo 请确保已下载正确版本的 WinDivert.dll:
    echo 1. 访问 https://www.reqrypt.org/windivert.html
    echo 2. 下载 WinDivert-2.2.2-A.zip
    echo 3. 解压并复制 x64/WinDivert.dll 到此目录
    pause
    exit /b 1
)

echo.
echo 2. 检查系统兼容性...
echo Windows版本:
systeminfo | findstr /B /C:"OS Name" /C:"OS Version"

echo.
echo 处理器架构:
echo %PROCESSOR_ARCHITECTURE%

echo.
echo 3. 检查驱动签名策略...
bcdedit /enum | findstr /i testsigning
if %errorLevel% == 0 (
    echo ✅ 测试签名已启用
) else (
    echo ⚠️  测试签名未启用
    echo 这可能导致 WinDivert 驱动加载失败
    echo.
    echo 要启用测试签名，请运行:
    echo bcdedit /set testsigning on
    echo 然后重启计算机
)

echo.
echo 4. 检查安全启动状态...
powershell -Command "Confirm-SecureBootUEFI" 2>nul
if %errorLevel% == 0 (
    echo ⚠️  安全启动已启用
    echo 这可能阻止 WinDivert 驱动加载
) else (
    echo ✅ 安全启动未启用或不支持
)

echo.
echo 5. 检查 Windows Defender 状态...
powershell -Command "Get-MpComputerStatus | Select-Object AntivirusEnabled, RealTimeProtectionEnabled"

echo.
echo 6. 检查事件日志中的驱动错误...
echo 检查系统事件日志中的 WinDivert 相关错误:
powershell -Command "Get-EventLog -LogName System -Newest 10 -EntryType Error | Where-Object {$_.Message -like '*WinDivert*'} | Select-Object TimeGenerated, EventID, Message"

echo.
echo 7. 尝试手动加载驱动...
echo 注意：这将尝试手动加载 WinDivert 驱动
choice /C YN /M "是否继续"
if errorlevel 2 goto :skip_driver

echo 尝试加载驱动...
sc create WinDivert binPath= "%CD%\WinDivert.sys" type= kernel start= demand 2>nul
sc start WinDivert 2>nul
if %errorLevel__ == 0 (
    echo ✅ 驱动加载成功
    sc stop WinDivert >nul 2>&1
    sc delete WinDivert >nul 2>&1
) else (
    echo ❌ 驱动加载失败
    sc delete WinDivert >nul 2>&1
)

:skip_driver

echo.
echo 8. 建议的解决方案...
echo.
echo 如果 WinDivert 无法工作，请尝试:
echo.
echo A. 下载不同版本的 WinDivert:
echo    - WinDivert 2.2.0 (较旧但更兼容)
echo    - WinDivert 1.4.3 (最稳定版本)
echo.
echo B. 系统配置:
echo    - 启用测试签名: bcdedit /set testsigning on
echo    - 禁用安全启动 (在 BIOS/UEFI 中)
echo    - 添加 Windows Defender 排除
echo.
echo C. 替代方案:
echo    - 使用 WinPcap/Npcap 替代
echo    - 使用用户模式网络库
echo.
echo ========================================
echo 检查完成
echo ========================================
pause
