@echo off
chcp 65001 >nul
title 修复 WinDivert 文件

echo ========================================
echo 修复 WinDivert 文件名问题
echo ========================================
echo.

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ 错误：需要管理员权限
    echo 请右键点击此脚本，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo ✅ 管理员权限检查通过
echo.

echo 当前目录: %CD%
echo.

echo 检查 WinDivert 文件...
echo ----------------------------------------

if exist WinDivert.dll (
    echo ✅ WinDivert.dll 存在
) else (
    echo ❌ WinDivert.dll 不存在
    goto :end
)

if exist WinDivert.sys (
    echo ✅ WinDivert.sys 已存在
    echo 无需修复
) else (
    echo ❌ WinDivert.sys 不存在
    
    if exist WinDivert64.sys (
        echo ✅ 找到 WinDivert64.sys
        echo 正在重命名为 WinDivert.sys...
        
        ren WinDivert64.sys WinDivert.sys
        if %errorLevel__ == 0 (
            echo ✅ 重命名成功
        ) else (
            echo ❌ 重命名失败
            goto :end
        )
    ) else (
        echo ❌ 也没有找到 WinDivert64.sys
        echo 请确保已下载完整的 WinDivert 包
        goto :end
    )
)

echo.
echo 最终文件检查:
echo ----------------------------------------
dir WinDivert.*

echo.
echo ✅ 文件修复完成
echo.

echo 现在测试 WinDivert...
if exist safe-test.exe (
    echo 运行测试程序...
    safe-test.exe
) else (
    echo safe-test.exe 不存在，请手动测试
)

:end
echo.
echo ========================================
echo 修复完成
echo ========================================
pause
