@echo off
echo ========================================
echo GoProxy 诊断脚本
echo ========================================
echo.

echo 1. 检查文件是否存在...
if exist goproxy.exe (
    echo ✓ goproxy.exe 存在
) else (
    echo ✗ goproxy.exe 不存在
    goto :end
)

echo.
echo 2. 检查管理员权限...
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✓ 具有管理员权限
) else (
    echo ✗ 需要管理员权限
    echo   请右键点击命令提示符，选择"以管理员身份运行"
    goto :end
)

echo.
echo 3. 检查Windows版本...
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
echo Windows版本: %VERSION%

echo.
echo 4. 检查系统架构...
if "%PROCESSOR_ARCHITECTURE%"=="AMD64" (
    echo ✓ 64位系统
) else (
    echo ✗ 需要64位Windows系统
)

echo.
echo 5. 测试程序启动（显示帮助）...
echo 运行: goproxy.exe -h
echo ----------------------------------------
goproxy.exe -h
echo ----------------------------------------
echo 退出代码: %ERRORLEVEL%

echo.
echo 6. 测试程序启动（显示版本信息）...
echo 运行: goproxy.exe （捕获输出）
echo ----------------------------------------
goproxy.exe > temp_output.log 2>&1
set EXIT_CODE=%ERRORLEVEL%
type temp_output.log
echo ----------------------------------------
echo 退出代码: %EXIT_CODE%

echo.
echo 7. 检查防病毒软件...
echo 请检查防病毒软件是否阻止了goproxy.exe
echo 建议将goproxy.exe添加到防病毒软件的白名单中

echo.
echo 8. 检查Windows Defender...
echo 如果Windows Defender阻止了程序，请添加例外

:end
echo.
echo 诊断完成。
if exist temp_output.log del temp_output.log
pause
