#!/bin/bash

# Build script for GoProxy (cross-compilation from Linux/macOS to Windows)

echo "Building GoProxy for Windows..."

# Set build variables
export GOOS=windows
export GOARCH=amd64
export CGO_ENABLED=0

# Get version from argument or use default
VERSION=${1:-"1.0.0"}

# Build with optimizations
go build -ldflags="-s -w -X main.appVersion=$VERSION" -o goproxy.exe ./cmd/goproxy

if [ $? -eq 0 ]; then
    echo ""
    echo "Build successful!"
    echo "Executable: goproxy.exe"
    echo ""
    echo "To run the proxy on Windows:"
    echo "  1. Transfer goproxy.exe to your Windows machine"
    echo "  2. Run as Administrator (required for packet capture)"
    echo "  3. Execute: goproxy.exe"
    echo ""
    echo "For help: goproxy.exe -h"
else
    echo ""
    echo "Build failed!"
    exit 1
fi
