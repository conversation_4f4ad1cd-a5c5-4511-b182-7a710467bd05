#!/bin/bash

# Build script for GoProxy (cross-compilation from Linux/macOS to Windows)

echo "Building GoProxy for Windows..."

# Set build variables
export GOOS=windows
export GOARCH=amd64
export CGO_ENABLED=0

# Get version from argument or use default
VERSION=${1:-"1.0.0"}

# Build main application
echo "Building main application..."
go build -ldflags="-s -w -X main.appVersion=$VERSION" -o goproxy.exe ./cmd/goproxy

if [ $? -ne 0 ]; then
    echo "Failed to build main application!"
    exit 1
fi

# Build fixed version with all improvements
echo "Building fixed version..."
go build -ldflags="-s -w -X main.appVersion=$VERSION" -o goproxy-fixed.exe ./cmd/goproxy

# Build test programs
echo "Building test programs..."
go build -ldflags="-s -w" -o fixed-test.exe ./cmd/fixed-test
go build -ldflags="-s -w" -o param-test.exe ./cmd/param-test
go build -ldflags="-s -w" -o api-test.exe ./cmd/api-test

echo ""
echo "Build successful!"
echo ""
echo "Built executables:"
echo "  - goproxy.exe (main application)"
echo "  - goproxy-fixed.exe (with all fixes applied)"
echo "  - fixed-test.exe (comprehensive test program)"
echo "  - param-test.exe (parameter testing)"
echo "  - api-test.exe (API testing)"
echo ""
echo "To run on Windows:"
echo "  1. Download WinDivert: run download-windivert.ps1 as Administrator"
echo "  2. Test fixes: run fixed-test.exe as Administrator"
echo "  3. Run proxy: run goproxy-fixed.exe as Administrator"
echo ""
echo "For help: goproxy-fixed.exe -h"
