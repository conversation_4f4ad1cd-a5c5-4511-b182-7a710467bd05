# GoProxy 快速开始指南

## 🚀 快速开始（3 步完成）

### 步骤 1：下载 WinDivert
**以管理员身份运行 PowerShell**，然后执行：
```powershell
.\download-windivert.ps1
```

### 步骤 2：测试修复
**以管理员身份运行命令提示符**，然后执行：
```cmd
.\fixed-test.exe
```

### 步骤 3：运行代理
**以管理员身份运行命令提示符**，然后执行：
```cmd
.\goproxy-fixed.exe
```

## 📋 详细说明

### 前提条件
- ✅ Windows 10/11 64位系统
- ✅ 管理员权限
- ✅ 网络连接（用于下载 WinDivert）

### 文件说明
- `goproxy-fixed.exe` - 修复后的主程序（推荐使用）
- `fixed-test.exe` - 综合测试程序
- `download-windivert.ps1` - 自动下载 WinDivert 脚本
- `config.yaml` - 配置文件

### 配置文件
编辑 `config.yaml` 来配置您的网络：

```yaml
# 要拦截的源子网（IPv4 CIDR 格式）
source_subnet: "***********/24"

# 目标 IPv6 地址（转发目的地）
target_ipv6: "fd7a:115c:a1e0:b1a:0:7:c0a8:601"

# 日志级别
log_level: "info"  # debug, info, warn, error
```

## 🔧 故障排除

### 问题 1：程序无法运行
**错误**：`指定的可执行文件不是此操作系统平台的有效应用程序`

**解决方案**：
1. 确保使用 Windows 版本的可执行文件
2. 如果需要重新编译，运行 `build.bat`

### 问题 2：WinDivert 加载失败
**错误**：`Failed to load WinDivert.dll`

**解决方案**：
1. 以管理员身份运行 `download-windivert.ps1`
2. 确保 `WinDivert.dll` 在程序目录中
3. 检查防病毒软件是否阻止

### 问题 3：权限不足
**错误**：`Access denied` 或 `需要管理员权限`

**解决方案**：
1. 右键点击命令提示符，选择"以管理员身份运行"
2. 右键点击程序，选择"以管理员身份运行"

### 问题 4：参数错误
**错误**：`The parameter is incorrect`

**解决方案**：
1. 运行 `fixed-test.exe` 验证修复
2. 检查配置文件格式
3. 确保使用修复后的版本 `goproxy-fixed.exe`

### 问题 5：防病毒软件阻止
**现象**：程序被删除或无法运行

**解决方案**：
1. 将程序目录添加到防病毒软件白名单
2. 临时禁用实时保护进行测试
3. 检查防病毒软件日志

## 📊 测试验证

### 运行测试程序
```cmd
# 以管理员身份运行
.\fixed-test.exe
```

**预期输出**：
```
WinDivert 修复测试程序
======================

✅ 管理员权限检查通过
✅ WinDivert 库加载成功
✅ WinDivertAddress 结构体测试通过
✅ 基本过滤器测试通过
...
```

### 检查日志
运行主程序时使用调试模式：
```cmd
.\goproxy-fixed.exe -log-level debug
```

## 🔄 重新编译（如果需要）

### 在 Windows 上编译
```cmd
# 运行编译脚本
.\build.bat

# 或手动编译
set GOOS=windows
set GOARCH=amd64
go build -o goproxy-fixed.exe ./cmd/goproxy
```

### 在 Linux/macOS 上交叉编译
```bash
# 运行编译脚本
./build.sh

# 或手动编译
GOOS=windows GOARCH=amd64 go build -o goproxy-fixed.exe ./cmd/goproxy
```

## 📞 获取帮助

### 查看程序帮助
```cmd
.\goproxy-fixed.exe -h
```

### 查看详细文档
- `FIXES-SUMMARY.md` - 修复总结
- `WINDIVERT-SETUP.md` - WinDivert 安装指南
- `TROUBLESHOOTING.md` - 故障排除指南

### 运行诊断
```cmd
.\diagnose.bat
```

## ✅ 成功标志

程序正常运行时，您应该看到：

```
╔═══════════════════════════════════════════════════════════════╗
║                          GoProxy v1.0.0                      ║
║                                                               ║
║           Transparent IPv4 to IPv6 Proxy for Windows         ║
║                                                               ║
║  Intercepts IPv4 traffic and forwards it to IPv6 addresses   ║
║  Perfect for Tailscale/Headscale networks with 4via6         ║
╚═══════════════════════════════════════════════════════════════╝

✅ 管理员权限检查通过
✅ 配置加载成功
✅ WinDivert 句柄打开成功
✅ 代理启动成功

代理正在运行。按 Ctrl+C 停止。
```

现在您的 GoProxy 应该可以正常工作了！🎉
