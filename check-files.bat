@echo off
chcp 65001 >nul
title 文件检查

echo ========================================
echo WinDivert 文件检查
echo ========================================
echo.

echo 当前目录: %CD%
echo.

echo 检查 WinDivert 相关文件:
echo ----------------------------------------

if exist WinDivert.dll (
    echo ✅ WinDivert.dll 存在
    dir WinDivert.dll
) else (
    echo ❌ WinDivert.dll 不存在
)

echo.

if exist WinDivert.sys (
    echo ✅ WinDivert.sys 存在
    dir WinDivert.sys
) else (
    echo ❌ WinDivert.sys 不存在
    echo    这是关键问题！WinDivert 需要 .sys 驱动文件
)

echo.

if exist WinDivert.lib (
    echo ✅ WinDivert.lib 存在
    dir WinDivert.lib
) else (
    echo ⚠️  WinDivert.lib 不存在 (可选文件)
)

echo.
echo 所有文件列表:
dir WinDivert.*

echo.
echo ----------------------------------------
echo 建议:
echo.

if not exist WinDivert.sys (
    echo ❌ 缺少 WinDivert.sys 文件
    echo.
    echo 解决方案:
    echo 1. 重新下载完整的 WinDivert 包
    echo 2. 从 https://www.reqrypt.org/windivert.html 下载
    echo 3. 解压后复制以下文件到此目录:
    echo    - x64\WinDivert.dll
    echo    - x64\WinDivert.sys  ← 重要！
    echo    - x64\WinDivert.lib  (可选)
) else (
    echo ✅ 文件完整
    echo.
    echo 如果仍有问题，可能是:
    echo 1. 驱动签名问题 - 运行: bcdedit /set testsigning on
    echo 2. 权限问题 - 确保以管理员身份运行
    echo 3. 版本兼容性 - 尝试 WinDivert 1.4.3 或 2.2.0
)

echo.
pause
