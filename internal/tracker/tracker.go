package tracker

import (
	"fmt"
	"net"
	"sync"
	"time"
)

// ConnectionState represents the state of a connection
type ConnectionState int

const (
	StateNew ConnectionState = iota
	StateEstablished
	StateClosing
	StateClosed
)

// String returns the string representation of the connection state
func (s ConnectionState) String() string {
	switch s {
	case StateNew:
		return "NEW"
	case StateEstablished:
		return "ESTABLISHED"
	case StateClosing:
		return "CLOSING"
	case StateClosed:
		return "CLOSED"
	default:
		return "UNKNOWN"
	}
}

// ConnectionKey uniquely identifies a connection
type ConnectionKey struct {
	Protocol string
	SrcIP    string
	SrcPort  uint16
	DstIP    string
	DstPort  uint16
}

// String returns a string representation of the connection key
func (k ConnectionKey) String() string {
	return fmt.Sprintf("%s:%s:%d->%s:%d", k.Protocol, k.SrcIP, k.SrcPort, k.DstIP, k.DstPort)
}

// Connection represents a tracked connection
type Connection struct {
	Key           ConnectionKey
	State         ConnectionState
	CreatedAt     time.Time
	LastActivity  time.Time
	BytesSent     uint64
	BytesReceived uint64
	PacketsSent   uint64
	PacketsReceived uint64
	
	// Translation information
	OriginalDstIP   net.IP
	TranslatedDstIP net.IP
}

// ConnectionTracker tracks active connections and their state
type ConnectionTracker struct {
	connections map[string]*Connection
	mutex       sync.RWMutex
	
	// Configuration
	maxConnections    int
	connectionTimeout time.Duration
	
	// Cleanup
	cleanupTicker *time.Ticker
	stopCleanup   chan bool
}

// NewConnectionTracker creates a new connection tracker
func NewConnectionTracker(maxConnections int, connectionTimeout time.Duration) *ConnectionTracker {
	tracker := &ConnectionTracker{
		connections:       make(map[string]*Connection),
		maxConnections:    maxConnections,
		connectionTimeout: connectionTimeout,
		stopCleanup:       make(chan bool),
	}

	// Start cleanup routine
	tracker.startCleanup()

	return tracker
}

// TrackConnection tracks a new or existing connection
func (t *ConnectionTracker) TrackConnection(protocol string, srcIP net.IP, srcPort uint16, dstIP net.IP, dstPort uint16, originalDstIP, translatedDstIP net.IP) (*Connection, error) {
	key := ConnectionKey{
		Protocol: protocol,
		SrcIP:    srcIP.String(),
		SrcPort:  srcPort,
		DstIP:    dstIP.String(),
		DstPort:  dstPort,
	}

	t.mutex.Lock()
	defer t.mutex.Unlock()

	// Check if connection already exists
	if conn, exists := t.connections[key.String()]; exists {
		conn.LastActivity = time.Now()
		return conn, nil
	}

	// Check connection limit
	if t.maxConnections > 0 && len(t.connections) >= t.maxConnections {
		return nil, fmt.Errorf("maximum connections (%d) reached", t.maxConnections)
	}

	// Create new connection
	conn := &Connection{
		Key:             key,
		State:           StateNew,
		CreatedAt:       time.Now(),
		LastActivity:    time.Now(),
		OriginalDstIP:   originalDstIP,
		TranslatedDstIP: translatedDstIP,
	}

	t.connections[key.String()] = conn
	return conn, nil
}

// GetConnection retrieves a connection by its key components
func (t *ConnectionTracker) GetConnection(protocol string, srcIP net.IP, srcPort uint16, dstIP net.IP, dstPort uint16) *Connection {
	key := ConnectionKey{
		Protocol: protocol,
		SrcIP:    srcIP.String(),
		SrcPort:  srcPort,
		DstIP:    dstIP.String(),
		DstPort:  dstPort,
	}

	t.mutex.RLock()
	defer t.mutex.RUnlock()

	return t.connections[key.String()]
}

// GetConnectionByKey retrieves a connection by its key
func (t *ConnectionTracker) GetConnectionByKey(key ConnectionKey) *Connection {
	t.mutex.RLock()
	defer t.mutex.RUnlock()

	return t.connections[key.String()]
}

// UpdateConnectionState updates the state of a connection
func (t *ConnectionTracker) UpdateConnectionState(key ConnectionKey, state ConnectionState) {
	t.mutex.Lock()
	defer t.mutex.Unlock()

	if conn, exists := t.connections[key.String()]; exists {
		conn.State = state
		conn.LastActivity = time.Now()
	}
}

// UpdateConnectionStats updates the statistics for a connection
func (t *ConnectionTracker) UpdateConnectionStats(key ConnectionKey, bytesSent, bytesReceived uint64, packetsSent, packetsReceived uint64) {
	t.mutex.Lock()
	defer t.mutex.Unlock()

	if conn, exists := t.connections[key.String()]; exists {
		conn.BytesSent += bytesSent
		conn.BytesReceived += bytesReceived
		conn.PacketsSent += packetsSent
		conn.PacketsReceived += packetsReceived
		conn.LastActivity = time.Now()
	}
}

// RemoveConnection removes a connection from tracking
func (t *ConnectionTracker) RemoveConnection(key ConnectionKey) {
	t.mutex.Lock()
	defer t.mutex.Unlock()

	delete(t.connections, key.String())
}

// GetAllConnections returns a copy of all tracked connections
func (t *ConnectionTracker) GetAllConnections() map[string]*Connection {
	t.mutex.RLock()
	defer t.mutex.RUnlock()

	connections := make(map[string]*Connection)
	for key, conn := range t.connections {
		// Create a copy of the connection
		connCopy := *conn
		connections[key] = &connCopy
	}

	return connections
}

// GetConnectionCount returns the number of tracked connections
func (t *ConnectionTracker) GetConnectionCount() int {
	t.mutex.RLock()
	defer t.mutex.RUnlock()

	return len(t.connections)
}

// GetConnectionsByState returns connections in a specific state
func (t *ConnectionTracker) GetConnectionsByState(state ConnectionState) []*Connection {
	t.mutex.RLock()
	defer t.mutex.RUnlock()

	var connections []*Connection
	for _, conn := range t.connections {
		if conn.State == state {
			connCopy := *conn
			connections = append(connections, &connCopy)
		}
	}

	return connections
}

// CleanupExpiredConnections removes expired connections
func (t *ConnectionTracker) CleanupExpiredConnections() int {
	t.mutex.Lock()
	defer t.mutex.Unlock()

	now := time.Now()
	var expiredKeys []string

	for key, conn := range t.connections {
		if now.Sub(conn.LastActivity) > t.connectionTimeout {
			expiredKeys = append(expiredKeys, key)
		}
	}

	for _, key := range expiredKeys {
		delete(t.connections, key)
	}

	return len(expiredKeys)
}

// startCleanup starts the background cleanup routine
func (t *ConnectionTracker) startCleanup() {
	t.cleanupTicker = time.NewTicker(30 * time.Second) // Cleanup every 30 seconds

	go func() {
		for {
			select {
			case <-t.cleanupTicker.C:
				cleaned := t.CleanupExpiredConnections()
				if cleaned > 0 {
					// Log cleanup if needed
					_ = cleaned
				}
			case <-t.stopCleanup:
				t.cleanupTicker.Stop()
				return
			}
		}
	}()
}

// Stop stops the connection tracker and cleanup routine
func (t *ConnectionTracker) Stop() {
	if t.cleanupTicker != nil {
		close(t.stopCleanup)
	}
}

// GetStats returns overall statistics
func (t *ConnectionTracker) GetStats() map[string]interface{} {
	t.mutex.RLock()
	defer t.mutex.RUnlock()

	stats := make(map[string]interface{})
	stats["total_connections"] = len(t.connections)
	stats["max_connections"] = t.maxConnections
	stats["connection_timeout"] = t.connectionTimeout.String()

	// Count by state
	stateCounts := make(map[string]int)
	var totalBytes, totalPackets uint64

	for _, conn := range t.connections {
		stateCounts[conn.State.String()]++
		totalBytes += conn.BytesSent + conn.BytesReceived
		totalPackets += conn.PacketsSent + conn.PacketsReceived
	}

	stats["connections_by_state"] = stateCounts
	stats["total_bytes"] = totalBytes
	stats["total_packets"] = totalPackets

	return stats
}

// SetConnectionTimeout updates the connection timeout
func (t *ConnectionTracker) SetConnectionTimeout(timeout time.Duration) {
	t.mutex.Lock()
	defer t.mutex.Unlock()
	t.connectionTimeout = timeout
}

// SetMaxConnections updates the maximum number of connections
func (t *ConnectionTracker) SetMaxConnections(max int) {
	t.mutex.Lock()
	defer t.mutex.Unlock()
	t.maxConnections = max
}
