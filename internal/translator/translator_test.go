package translator

import (
	"net"
	"testing"
)

func TestNewAddressTranslator(t *testing.T) {
	tests := []struct {
		name         string
		sourceSubnet string
		targetIPv6   string
		expectError  bool
	}{
		{
			name:         "Valid configuration",
			sourceSubnet: "***********/24",
			targetIPv6:   "fd7a:115c:a1e0:b1a:0:7:c0a8:601",
			expectError:  false,
		},
		{
			name:         "Invalid subnet",
			sourceSubnet: "invalid-subnet",
			targetIPv6:   "fd7a:115c:a1e0:b1a:0:7:c0a8:601",
			expectError:  true,
		},
		{
			name:         "Invalid IPv6 address",
			sourceSubnet: "***********/24",
			targetIPv6:   "***********", // IPv4 instead of IPv6
			expectError:  true,
		},
		{
			name:         "Empty IPv6 address",
			sourceSubnet: "***********/24",
			targetIPv6:   "",
			expectError:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			translator, err := NewAddressTranslator(tt.sourceSubnet, tt.targetIPv6)
			
			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error but got none")
				}
				if translator != nil {
					t.Errorf("Expected nil translator on error")
				}
			} else {
				if err != nil {
					t.Errorf("Expected no error but got: %v", err)
				}
				if translator == nil {
					t.Errorf("Expected valid translator but got nil")
				}
			}
		})
	}
}

func TestShouldIntercept(t *testing.T) {
	translator, err := NewAddressTranslator("***********/24", "fd7a:115c:a1e0:b1a:0:7:c0a8:601")
	if err != nil {
		t.Fatalf("Failed to create translator: %v", err)
	}

	tests := []struct {
		name        string
		ip          string
		shouldIntercept bool
	}{
		{
			name:        "IP in subnet",
			ip:          "***********00",
			shouldIntercept: true,
		},
		{
			name:        "First IP in subnet",
			ip:          "***********",
			shouldIntercept: true,
		},
		{
			name:        "Last IP in subnet",
			ip:          "*************",
			shouldIntercept: true,
		},
		{
			name:        "IP outside subnet",
			ip:          "*************",
			shouldIntercept: false,
		},
		{
			name:        "Different network",
			ip:          "********",
			shouldIntercept: false,
		},
		{
			name:        "Localhost",
			ip:          "127.0.0.1",
			shouldIntercept: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ip := net.ParseIP(tt.ip)
			if ip == nil {
				t.Fatalf("Invalid IP address: %s", tt.ip)
			}

			result := translator.ShouldIntercept(ip)
			if result != tt.shouldIntercept {
				t.Errorf("Expected ShouldIntercept(%s) = %v, got %v", 
					tt.ip, tt.shouldIntercept, result)
			}
		})
	}
}

func TestTranslateIPv4ToIPv6(t *testing.T) {
	translator, err := NewAddressTranslator("***********/24", "fd7a:115c:a1e0:b1a:0:7:c0a8:601")
	if err != nil {
		t.Fatalf("Failed to create translator: %v", err)
	}

	tests := []struct {
		name     string
		ipv4     string
		expectNil bool
	}{
		{
			name:     "Valid IP in subnet",
			ipv4:     "***********00",
			expectNil: false,
		},
		{
			name:     "IP outside subnet",
			ipv4:     "********",
			expectNil: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ipv4 := net.ParseIP(tt.ipv4)
			if ipv4 == nil {
				t.Fatalf("Invalid IPv4 address: %s", tt.ipv4)
			}

			ipv6 := translator.TranslateIPv4ToIPv6(ipv4)
			
			if tt.expectNil {
				if ipv6 != nil {
					t.Errorf("Expected nil IPv6 address for %s, got %s", tt.ipv4, ipv6)
				}
			} else {
				if ipv6 == nil {
					t.Errorf("Expected valid IPv6 address for %s, got nil", tt.ipv4)
				}
				
				// Verify it's the target IPv6 address
				expectedIPv6 := net.ParseIP("fd7a:115c:a1e0:b1a:0:7:c0a8:601")
				if !ipv6.Equal(expectedIPv6) {
					t.Errorf("Expected IPv6 %s, got %s", expectedIPv6, ipv6)
				}
			}
		})
	}
}

func TestTranslateIPv6ToIPv4(t *testing.T) {
	translator, err := NewAddressTranslator("***********/24", "fd7a:115c:a1e0:b1a:0:7:c0a8:601")
	if err != nil {
		t.Fatalf("Failed to create translator: %v", err)
	}

	// First create a mapping
	ipv4 := net.ParseIP("***********00")
	ipv6 := translator.TranslateIPv4ToIPv6(ipv4)
	if ipv6 == nil {
		t.Fatalf("Failed to create IPv4 to IPv6 mapping")
	}

	// Now test reverse translation
	reversedIPv4 := translator.TranslateIPv6ToIPv4(ipv6)
	if reversedIPv4 == nil {
		t.Errorf("Failed to reverse translate IPv6 to IPv4")
	} else if !reversedIPv4.Equal(ipv4) {
		t.Errorf("Reverse translation mismatch: expected %s, got %s", ipv4, reversedIPv4)
	}

	// Test with unmapped IPv6
	unmappedIPv6 := net.ParseIP("2001:db8::1")
	result := translator.TranslateIPv6ToIPv4(unmappedIPv6)
	if result != nil {
		t.Errorf("Expected nil for unmapped IPv6, got %s", result)
	}
}

func TestGetMappingCount(t *testing.T) {
	translator, err := NewAddressTranslator("***********/24", "fd7a:115c:a1e0:b1a:0:7:c0a8:601")
	if err != nil {
		t.Fatalf("Failed to create translator: %v", err)
	}

	// Initially should be 0
	if count := translator.GetMappingCount(); count != 0 {
		t.Errorf("Expected initial mapping count 0, got %d", count)
	}

	// Add some mappings
	ips := []string{"***********00", "***********01", "***********02"}
	for _, ipStr := range ips {
		ip := net.ParseIP(ipStr)
		translator.TranslateIPv4ToIPv6(ip)
	}

	if count := translator.GetMappingCount(); count != len(ips) {
		t.Errorf("Expected mapping count %d, got %d", len(ips), count)
	}
}

func TestClearMappings(t *testing.T) {
	translator, err := NewAddressTranslator("***********/24", "fd7a:115c:a1e0:b1a:0:7:c0a8:601")
	if err != nil {
		t.Fatalf("Failed to create translator: %v", err)
	}

	// Add a mapping
	ip := net.ParseIP("***********00")
	translator.TranslateIPv4ToIPv6(ip)

	if count := translator.GetMappingCount(); count != 1 {
		t.Errorf("Expected mapping count 1, got %d", count)
	}

	// Clear mappings
	translator.ClearMappings()

	if count := translator.GetMappingCount(); count != 0 {
		t.Errorf("Expected mapping count 0 after clear, got %d", count)
	}
}

func TestUpdateTargetIPv6(t *testing.T) {
	translator, err := NewAddressTranslator("***********/24", "fd7a:115c:a1e0:b1a:0:7:c0a8:601")
	if err != nil {
		t.Fatalf("Failed to create translator: %v", err)
	}

	// Add a mapping
	ip := net.ParseIP("***********00")
	translator.TranslateIPv4ToIPv6(ip)

	if count := translator.GetMappingCount(); count != 1 {
		t.Errorf("Expected mapping count 1, got %d", count)
	}

	// Update target IPv6
	newTarget := "2001:db8::1"
	err = translator.UpdateTargetIPv6(newTarget)
	if err != nil {
		t.Errorf("Failed to update target IPv6: %v", err)
	}

	// Mappings should be cleared
	if count := translator.GetMappingCount(); count != 0 {
		t.Errorf("Expected mapping count 0 after target update, got %d", count)
	}

	// New target should be used
	expectedTarget := net.ParseIP(newTarget)
	actualTarget := translator.GetTargetIPv6()
	if !actualTarget.Equal(expectedTarget) {
		t.Errorf("Expected target IPv6 %s, got %s", expectedTarget, actualTarget)
	}
}

func TestUpdateSourceSubnet(t *testing.T) {
	translator, err := NewAddressTranslator("***********/24", "fd7a:115c:a1e0:b1a:0:7:c0a8:601")
	if err != nil {
		t.Fatalf("Failed to create translator: %v", err)
	}

	// Add a mapping
	ip := net.ParseIP("***********00")
	translator.TranslateIPv4ToIPv6(ip)

	if count := translator.GetMappingCount(); count != 1 {
		t.Errorf("Expected mapping count 1, got %d", count)
	}

	// Update source subnet
	newSubnet := "10.0.0.0/24"
	err = translator.UpdateSourceSubnet(newSubnet)
	if err != nil {
		t.Errorf("Failed to update source subnet: %v", err)
	}

	// Mappings should be cleared
	if count := translator.GetMappingCount(); count != 0 {
		t.Errorf("Expected mapping count 0 after subnet update, got %d", count)
	}

	// Old IP should no longer be intercepted
	if translator.ShouldIntercept(ip) {
		t.Errorf("Old IP should not be intercepted after subnet change")
	}

	// New subnet IP should be intercepted
	newIP := net.ParseIP("**********")
	if !translator.ShouldIntercept(newIP) {
		t.Errorf("New subnet IP should be intercepted")
	}
}
