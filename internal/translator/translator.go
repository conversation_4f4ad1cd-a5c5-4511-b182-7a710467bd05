package translator

import (
	"fmt"
	"net"
	"sync"
)

// AddressTranslator handles IPv4 to IPv6 address translation
type AddressTranslator struct {
	sourceSubnet *net.IPNet
	targetIPv6   net.IP
	
	// Translation mappings
	ipv4ToIPv6 map[string]net.IP
	ipv6ToIPv4 map[string]net.IP
	mutex      sync.RWMutex
}

// NewAddressTranslator creates a new address translator
func NewAddressTranslator(sourceSubnet string, targetIPv6 string) (*AddressTranslator, error) {
	// Parse source subnet
	_, subnet, err := net.ParseCIDR(sourceSubnet)
	if err != nil {
		return nil, fmt.Errorf("invalid source subnet %q: %w", sourceSubnet, err)
	}

	// Parse target IPv6 address
	ipv6 := net.ParseIP(targetIPv6)
	if ipv6 == nil || ipv6.To4() != nil {
		return nil, fmt.Errorf("invalid IPv6 address %q", targetIPv6)
	}

	return &AddressTranslator{
		sourceSubnet: subnet,
		targetIPv6:   ipv6,
		ipv4ToIPv6:   make(map[string]net.IP),
		ipv6ToIPv4:   make(map[string]net.IP),
	}, nil
}

// ShouldIntercept checks if an IPv4 address should be intercepted
func (t *AddressTranslator) ShouldIntercept(ipv4 net.IP) bool {
	return t.sourceSubnet.Contains(ipv4)
}

// TranslateIPv4ToIPv6 translates an IPv4 address to the target IPv6 address
func (t *AddressTranslator) TranslateIPv4ToIPv6(ipv4 net.IP) net.IP {
	if !t.ShouldIntercept(ipv4) {
		return nil
	}

	t.mutex.Lock()
	defer t.mutex.Unlock()

	ipv4Str := ipv4.String()
	
	// Check if we already have a mapping
	if ipv6, exists := t.ipv4ToIPv6[ipv4Str]; exists {
		return ipv6
	}

	// For now, we map all IPv4 addresses in the subnet to the same IPv6 address
	// In a more sophisticated implementation, we could create unique mappings
	// or embed the IPv4 address in the IPv6 address
	ipv6 := make(net.IP, len(t.targetIPv6))
	copy(ipv6, t.targetIPv6)

	// Store the mapping
	t.ipv4ToIPv6[ipv4Str] = ipv6
	t.ipv6ToIPv4[ipv6.String()] = ipv4

	return ipv6
}

// TranslateIPv6ToIPv4 translates an IPv6 address back to IPv4
func (t *AddressTranslator) TranslateIPv6ToIPv4(ipv6 net.IP) net.IP {
	t.mutex.RLock()
	defer t.mutex.RUnlock()

	ipv6Str := ipv6.String()
	if ipv4, exists := t.ipv6ToIPv4[ipv6Str]; exists {
		return ipv4
	}

	return nil
}

// GetSourceSubnet returns the source subnet being intercepted
func (t *AddressTranslator) GetSourceSubnet() *net.IPNet {
	return t.sourceSubnet
}

// GetTargetIPv6 returns the target IPv6 address
func (t *AddressTranslator) GetTargetIPv6() net.IP {
	return t.targetIPv6
}

// GetMappingCount returns the number of active address mappings
func (t *AddressTranslator) GetMappingCount() int {
	t.mutex.RLock()
	defer t.mutex.RUnlock()
	return len(t.ipv4ToIPv6)
}

// ClearMappings clears all address mappings
func (t *AddressTranslator) ClearMappings() {
	t.mutex.Lock()
	defer t.mutex.Unlock()
	
	t.ipv4ToIPv6 = make(map[string]net.IP)
	t.ipv6ToIPv4 = make(map[string]net.IP)
}

// GetAllMappings returns a copy of all current mappings
func (t *AddressTranslator) GetAllMappings() map[string]string {
	t.mutex.RLock()
	defer t.mutex.RUnlock()

	mappings := make(map[string]string)
	for ipv4Str, ipv6 := range t.ipv4ToIPv6 {
		mappings[ipv4Str] = ipv6.String()
	}

	return mappings
}

// RemoveMapping removes a specific IPv4 to IPv6 mapping
func (t *AddressTranslator) RemoveMapping(ipv4 net.IP) {
	t.mutex.Lock()
	defer t.mutex.Unlock()

	ipv4Str := ipv4.String()
	if ipv6, exists := t.ipv4ToIPv6[ipv4Str]; exists {
		delete(t.ipv4ToIPv6, ipv4Str)
		delete(t.ipv6ToIPv4, ipv6.String())
	}
}

// UpdateTargetIPv6 updates the target IPv6 address and clears existing mappings
func (t *AddressTranslator) UpdateTargetIPv6(targetIPv6 string) error {
	ipv6 := net.ParseIP(targetIPv6)
	if ipv6 == nil || ipv6.To4() != nil {
		return fmt.Errorf("invalid IPv6 address %q", targetIPv6)
	}

	t.mutex.Lock()
	defer t.mutex.Unlock()

	t.targetIPv6 = ipv6
	// Clear existing mappings since they point to the old target
	t.ipv4ToIPv6 = make(map[string]net.IP)
	t.ipv6ToIPv4 = make(map[string]net.IP)

	return nil
}

// UpdateSourceSubnet updates the source subnet and clears existing mappings
func (t *AddressTranslator) UpdateSourceSubnet(sourceSubnet string) error {
	_, subnet, err := net.ParseCIDR(sourceSubnet)
	if err != nil {
		return fmt.Errorf("invalid source subnet %q: %w", sourceSubnet, err)
	}

	t.mutex.Lock()
	defer t.mutex.Unlock()

	t.sourceSubnet = subnet
	// Clear existing mappings since they may no longer be valid
	t.ipv4ToIPv6 = make(map[string]net.IP)
	t.ipv6ToIPv4 = make(map[string]net.IP)

	return nil
}
