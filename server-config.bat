@echo off
chcp 65001 >nul
title Windows Server 2019 配置检查

echo ========================================
echo Windows Server 2019 配置检查
echo ========================================
echo.

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ 错误：需要管理员权限
    echo 请右键点击此脚本，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo ✅ 管理员权限检查通过
echo.

echo 1. 检查 Windows 版本...
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
echo Windows版本: %VERSION%

REM 检查是否是 Windows Server
systeminfo | findstr /C:"Windows Server" >nul
if %errorLevel% == 0 (
    echo ✅ 检测到 Windows Server
) else (
    echo ⚠️  未检测到 Windows Server
)

echo.
echo 2. 检查执行策略...
powershell -Command "Get-ExecutionPolicy"

echo.
echo 3. 检查 Windows Defender 状态...
powershell -Command "Get-MpComputerStatus | Select-Object AntivirusEnabled, RealTimeProtectionEnabled"

echo.
echo 4. 检查防火墙状态...
netsh advfirewall show allprofiles state

echo.
echo 5. 尝试禁用实时保护（临时）...
echo 注意：这将临时禁用 Windows Defender 实时保护
choice /C YN /M "是否继续"
if errorlevel 2 goto :skip_defender

powershell -Command "Set-MpPreference -DisableRealtimeMonitoring $true"
echo ✅ 实时保护已临时禁用

:skip_defender

echo.
echo 6. 添加排除路径...
set CURRENT_DIR=%CD%
echo 添加当前目录到 Windows Defender 排除列表: %CURRENT_DIR%
powershell -Command "Add-MpPreference -ExclusionPath '%CURRENT_DIR%'"

echo.
echo 7. 检查文件阻止状态...
powershell -Command "Get-ChildItem *.exe | Get-Item -Stream Zone.Identifier -ErrorAction SilentlyContinue"

echo.
echo 8. 解除文件阻止...
powershell -Command "Get-ChildItem *.exe | Unblock-File"
echo ✅ 已解除所有 .exe 文件的阻止状态

echo.
echo 9. 设置兼容性模式...
echo 为 Windows Server 2019 设置兼容性...

echo.
echo ========================================
echo 配置完成
echo ========================================
echo.
echo 现在尝试运行程序：
echo 1. minimal-test.exe
echo 2. goproxy.exe
echo.

if exist minimal-test.exe (
    echo 尝试运行 minimal-test.exe...
    minimal-test.exe
) else (
    echo ❌ minimal-test.exe 不存在
)

echo.
echo 如果需要重新启用实时保护：
echo powershell -Command "Set-MpPreference -DisableRealtimeMonitoring $false"
echo.
pause
