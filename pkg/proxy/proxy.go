package proxy

import (
	"context"
	"fmt"
	"net"
	"runtime"
	"sync"
	"time"

	"goproxy/internal/tracker"
	"goproxy/internal/translator"
	"goproxy/pkg/config"
	"goproxy/pkg/logger"
	"goproxy/pkg/windivert"
)

// Proxy represents the main transparent proxy
type Proxy struct {
	config     *config.Config
	logger     *logger.Logger
	translator *translator.AddressTranslator
	tracker    *tracker.ConnectionTracker

	// WinDivert handles
	outboundHandle *windivert.Handle
	inboundHandle  *windivert.Handle

	// Control
	ctx     context.Context
	cancel  context.CancelFunc
	wg      sync.WaitGroup
	running bool
	mutex   sync.RWMutex

	// Statistics
	stats ProxyStats
}

// ProxyStats holds proxy statistics
type ProxyStats struct {
	PacketsIntercepted uint64
	PacketsForwarded   uint64
	PacketsDropped     uint64
	BytesTransferred   uint64
	ConnectionsActive  uint64
	ConnectionsTotal   uint64
	ErrorsTotal        uint64
	StartTime          time.Time
	mutex              sync.RWMutex
}

// NewProxy creates a new transparent proxy instance
func NewProxy(cfg *config.Config) (*Proxy, error) {
	// Validate platform
	if runtime.GOOS != "windows" {
		return nil, fmt.Errorf("transparent proxy is only supported on Windows")
	}

	// Create logger
	log := logger.NewFromString(cfg.LogLevel, nil)

	// Create address translator
	trans, err := translator.NewAddressTranslator(cfg.SourceSubnet, cfg.TargetIPv6)
	if err != nil {
		return nil, fmt.Errorf("failed to create address translator: %w", err)
	}

	// Create connection tracker
	connectionTimeout := time.Duration(cfg.ConnectionTimeout) * time.Second
	track := tracker.NewConnectionTracker(cfg.MaxConnections, connectionTimeout)

	// Create context
	ctx, cancel := context.WithCancel(context.Background())

	proxy := &Proxy{
		config:     cfg,
		logger:     log,
		translator: trans,
		tracker:    track,
		ctx:        ctx,
		cancel:     cancel,
		stats: ProxyStats{
			StartTime: time.Now(),
		},
	}

	return proxy, nil
}

// Start starts the transparent proxy
func (p *Proxy) Start() error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if p.running {
		return fmt.Errorf("proxy is already running")
	}

	p.logger.Info("Starting transparent proxy")
	p.logger.Info("Source subnet: %s", p.config.SourceSubnet)
	p.logger.Info("Target IPv6: %s", p.config.TargetIPv6)

	// Open WinDivert handles
	if err := p.openWinDivertHandles(); err != nil {
		return fmt.Errorf("failed to open WinDivert handles: %w", err)
	}

	p.running = true

	// Start packet processing goroutines
	p.wg.Add(2)
	go p.processOutboundPackets()
	go p.processInboundPackets()

	p.logger.Info("Transparent proxy started successfully")
	return nil
}

// Stop stops the transparent proxy
func (p *Proxy) Stop() error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if !p.running {
		return nil
	}

	p.logger.Info("Stopping transparent proxy")

	// Cancel context to signal goroutines to stop
	p.cancel()

	// Close WinDivert handles
	p.closeWinDivertHandles()

	// Wait for goroutines to finish
	p.wg.Wait()

	// Stop connection tracker
	p.tracker.Stop()

	p.running = false
	p.logger.Info("Transparent proxy stopped")

	return nil
}

// IsRunning returns whether the proxy is currently running
func (p *Proxy) IsRunning() bool {
	p.mutex.RLock()
	defer p.mutex.RUnlock()
	return p.running
}

// GetStats returns current proxy statistics
func (p *Proxy) GetStats() ProxyStats {
	p.stats.mutex.RLock()
	defer p.stats.mutex.RUnlock()

	stats := p.stats
	stats.ConnectionsActive = uint64(p.tracker.GetConnectionCount())

	return stats
}

// openWinDivertHandles opens the WinDivert handles for packet capture
func (p *Proxy) openWinDivertHandles() error {
	// Create filter for outbound packets to our target subnet
	outboundFilter := fmt.Sprintf("outbound and ip.DstAddr >= %s and ip.DstAddr <= %s",
		p.getSubnetStart(), p.getSubnetEnd())

	// Open outbound handle
	var err error
	p.outboundHandle, err = windivert.Open(
		outboundFilter,
		windivert.WINDIVERT_LAYER_NETWORK,
		0, // priority
		0, // flags
	)
	if err != nil {
		return fmt.Errorf("failed to open outbound WinDivert handle: %w", err)
	}

	// Create filter for inbound packets from our target IPv6
	inboundFilter := fmt.Sprintf("inbound and ipv6.SrcAddr == %s", p.config.TargetIPv6)

	// Open inbound handle
	p.inboundHandle, err = windivert.Open(
		inboundFilter,
		windivert.WINDIVERT_LAYER_NETWORK,
		0, // priority
		0, // flags
	)
	if err != nil {
		p.outboundHandle.Close()
		return fmt.Errorf("failed to open inbound WinDivert handle: %w", err)
	}

	p.logger.Debug("WinDivert handles opened successfully")
	p.logger.Debug("Outbound filter: %s", outboundFilter)
	p.logger.Debug("Inbound filter: %s", inboundFilter)

	return nil
}

// closeWinDivertHandles closes the WinDivert handles
func (p *Proxy) closeWinDivertHandles() {
	if p.outboundHandle != nil {
		p.outboundHandle.Close()
		p.outboundHandle = nil
	}
	if p.inboundHandle != nil {
		p.inboundHandle.Close()
		p.inboundHandle = nil
	}
	p.logger.Debug("WinDivert handles closed")
}

// getSubnetStart returns the first IP address in the subnet
func (p *Proxy) getSubnetStart() string {
	subnet := p.translator.GetSourceSubnet()
	ip := subnet.IP.Mask(subnet.Mask)
	return ip.String()
}

// getSubnetEnd returns the last IP address in the subnet
func (p *Proxy) getSubnetEnd() string {
	subnet := p.translator.GetSourceSubnet()

	// Calculate broadcast address
	ip := make(net.IP, len(subnet.IP))
	copy(ip, subnet.IP)

	for i := 0; i < len(ip); i++ {
		ip[i] |= ^subnet.Mask[i]
	}

	return ip.String()
}

// incrementStat safely increments a statistic
func (p *Proxy) incrementStat(stat *uint64) {
	p.stats.mutex.Lock()
	defer p.stats.mutex.Unlock()
	*stat++
}

// addToStat safely adds to a statistic
func (p *Proxy) addToStat(stat *uint64, value uint64) {
	p.stats.mutex.Lock()
	defer p.stats.mutex.Unlock()
	*stat += value
}

// processOutboundPackets processes outbound IPv4 packets and translates them to IPv6
func (p *Proxy) processOutboundPackets() {
	defer p.wg.Done()

	buffer := make([]byte, p.config.BufferSize)

	p.logger.Debug("Started outbound packet processing")

	for {
		select {
		case <-p.ctx.Done():
			p.logger.Debug("Stopping outbound packet processing")
			return
		default:
			// Receive packet
			length, addr, err := p.outboundHandle.Recv(buffer)
			if err != nil {
				p.logger.Error("Failed to receive outbound packet: %v", err)
				p.incrementStat(&p.stats.ErrorsTotal)
				continue
			}

			p.incrementStat(&p.stats.PacketsIntercepted)

			// Process the packet
			if err := p.processOutboundPacket(buffer[:length], addr); err != nil {
				p.logger.Debug("Failed to process outbound packet: %v", err)
				p.incrementStat(&p.stats.PacketsDropped)
			} else {
				p.incrementStat(&p.stats.PacketsForwarded)
				p.addToStat(&p.stats.BytesTransferred, uint64(length))
			}
		}
	}
}

// processInboundPackets processes inbound IPv6 packets and translates them back to IPv4
func (p *Proxy) processInboundPackets() {
	defer p.wg.Done()

	buffer := make([]byte, p.config.BufferSize)

	p.logger.Debug("Started inbound packet processing")

	for {
		select {
		case <-p.ctx.Done():
			p.logger.Debug("Stopping inbound packet processing")
			return
		default:
			// Receive packet
			length, addr, err := p.inboundHandle.Recv(buffer)
			if err != nil {
				p.logger.Error("Failed to receive inbound packet: %v", err)
				p.incrementStat(&p.stats.ErrorsTotal)
				continue
			}

			p.incrementStat(&p.stats.PacketsIntercepted)

			// Process the packet
			if err := p.processInboundPacket(buffer[:length], addr); err != nil {
				p.logger.Debug("Failed to process inbound packet: %v", err)
				p.incrementStat(&p.stats.PacketsDropped)
			} else {
				p.incrementStat(&p.stats.PacketsForwarded)
				p.addToStat(&p.stats.BytesTransferred, uint64(length))
			}
		}
	}
}

// processOutboundPacket processes a single outbound IPv4 packet
func (p *Proxy) processOutboundPacket(packet []byte, addr *windivert.WinDivertAddress) error {
	// Parse the packet
	ipv4Hdr, _, tcpHdr, udpHdr, err := windivert.HelperParsePacket(packet)
	if err != nil {
		return fmt.Errorf("failed to parse packet: %w", err)
	}

	if ipv4Hdr == nil {
		return fmt.Errorf("not an IPv4 packet")
	}

	// Get source and destination IPs
	srcIP := ipv4Hdr.GetSrcIP()
	dstIP := ipv4Hdr.GetDstIP()

	// Check if we should intercept this packet
	if !p.translator.ShouldIntercept(dstIP) {
		// Forward packet unchanged
		_, err := p.outboundHandle.Send(packet, addr)
		return err
	}

	// Translate destination IP to IPv6
	targetIPv6 := p.translator.TranslateIPv4ToIPv6(dstIP)
	if targetIPv6 == nil {
		return fmt.Errorf("failed to translate IPv4 address %s", dstIP)
	}

	// Track the connection
	var srcPort, dstPort uint16
	protocol := "unknown"

	if tcpHdr != nil {
		srcPort = tcpHdr.SrcPort
		dstPort = tcpHdr.DstPort
		protocol = "tcp"
	} else if udpHdr != nil {
		srcPort = udpHdr.SrcPort
		dstPort = udpHdr.DstPort
		protocol = "udp"
	}

	conn, err := p.tracker.TrackConnection(protocol, srcIP, srcPort, targetIPv6, dstPort, dstIP, targetIPv6)
	if err != nil {
		return fmt.Errorf("failed to track connection: %w", err)
	}

	p.logger.Debug("Translating outbound packet: %s:%d -> %s:%d (IPv6: %s)",
		srcIP, srcPort, dstIP, dstPort, targetIPv6)

	// Create IPv6 packet
	ipv6Packet, err := p.createIPv6Packet(packet, ipv4Hdr, srcIP, targetIPv6)
	if err != nil {
		return fmt.Errorf("failed to create IPv6 packet: %w", err)
	}

	// Update connection stats
	p.tracker.UpdateConnectionStats(conn.Key, uint64(len(packet)), 0, 1, 0)

	// Send the IPv6 packet
	addr.IPv6 = 1 // Mark as IPv6
	_, err = p.outboundHandle.Send(ipv6Packet, addr)
	if err != nil {
		return fmt.Errorf("failed to send IPv6 packet: %w", err)
	}

	return nil
}

// processInboundPacket processes a single inbound IPv6 packet
func (p *Proxy) processInboundPacket(packet []byte, addr *windivert.WinDivertAddress) error {
	// Parse the packet
	_, ipv6Hdr, tcpHdr, udpHdr, err := windivert.HelperParsePacket(packet)
	if err != nil {
		return fmt.Errorf("failed to parse packet: %w", err)
	}

	if ipv6Hdr == nil {
		return fmt.Errorf("not an IPv6 packet")
	}

	// Get source and destination IPs
	srcIPv6 := ipv6Hdr.GetSrcIP()
	dstIPv6 := ipv6Hdr.GetDstIP()

	// Translate source IPv6 back to IPv4
	originalDstIP := p.translator.TranslateIPv6ToIPv4(srcIPv6)
	if originalDstIP == nil {
		return fmt.Errorf("no mapping found for IPv6 address %s", srcIPv6)
	}

	// Track the connection
	var srcPort, dstPort uint16
	protocol := "unknown"

	if tcpHdr != nil {
		srcPort = tcpHdr.SrcPort
		dstPort = tcpHdr.DstPort
		protocol = "tcp"
	} else if udpHdr != nil {
		srcPort = udpHdr.SrcPort
		dstPort = udpHdr.DstPort
		protocol = "udp"
	}

	p.logger.Debug("Translating inbound packet: %s:%d -> %s:%d (original IPv4: %s)",
		srcIPv6, srcPort, dstIPv6, dstPort, originalDstIP)

	// Create IPv4 packet
	ipv4Packet, err := p.createIPv4Packet(packet, ipv6Hdr, originalDstIP, dstIPv6)
	if err != nil {
		return fmt.Errorf("failed to create IPv4 packet: %w", err)
	}

	// Update connection stats
	connKey := tracker.ConnectionKey{
		Protocol: protocol,
		SrcIP:    dstIPv6.String(),
		SrcPort:  dstPort,
		DstIP:    srcIPv6.String(),
		DstPort:  srcPort,
	}
	p.tracker.UpdateConnectionStats(connKey, 0, uint64(len(packet)), 0, 1)

	// Send the IPv4 packet
	addr.IPv6 = 0 // Mark as IPv4
	_, err = p.inboundHandle.Send(ipv4Packet, addr)
	if err != nil {
		return fmt.Errorf("failed to send IPv4 packet: %w", err)
	}

	return nil
}

// createIPv6Packet creates an IPv6 packet from an IPv4 packet
func (p *Proxy) createIPv6Packet(ipv4Packet []byte, ipv4Hdr *windivert.IPv4Header, srcIP, dstIP net.IP) ([]byte, error) {
	// Calculate payload size (IPv4 packet minus IPv4 header)
	ipv4HdrLen := int(ipv4Hdr.HdrLength) * 4
	payloadSize := len(ipv4Packet) - ipv4HdrLen

	// Create IPv6 header (40 bytes)
	ipv6Packet := make([]byte, 40+payloadSize)

	// IPv6 header fields
	ipv6Packet[0] = 0x60 // Version (4 bits) + Traffic Class (4 bits)
	ipv6Packet[1] = 0x00 // Traffic Class (4 bits) + Flow Label (4 bits)
	ipv6Packet[2] = 0x00 // Flow Label (8 bits)
	ipv6Packet[3] = 0x00 // Flow Label (8 bits)

	// Payload length (16 bits, big endian)
	ipv6Packet[4] = byte(payloadSize >> 8)
	ipv6Packet[5] = byte(payloadSize)

	// Next header (same as IPv4 protocol)
	ipv6Packet[6] = ipv4Hdr.Protocol

	// Hop limit (same as IPv4 TTL)
	ipv6Packet[7] = ipv4Hdr.TTL

	// Source address (16 bytes)
	copy(ipv6Packet[8:24], srcIP.To16())

	// Destination address (16 bytes)
	copy(ipv6Packet[24:40], dstIP.To16())

	// Copy payload from IPv4 packet
	copy(ipv6Packet[40:], ipv4Packet[ipv4HdrLen:])

	return ipv6Packet, nil
}

// createIPv4Packet creates an IPv4 packet from an IPv6 packet
func (p *Proxy) createIPv4Packet(ipv6Packet []byte, ipv6Hdr *windivert.IPv6Header, srcIP, dstIP net.IP) ([]byte, error) {
	// Calculate payload size (IPv6 packet minus IPv6 header)
	payloadSize := len(ipv6Packet) - 40

	// Create IPv4 header (20 bytes minimum)
	ipv4Packet := make([]byte, 20+payloadSize)

	// IPv4 header fields
	ipv4Packet[0] = 0x45 // Version (4 bits) + Header Length (4 bits) = 5 * 4 = 20 bytes
	ipv4Packet[1] = 0x00 // Type of Service

	// Total length (16 bits, big endian)
	totalLen := 20 + payloadSize
	ipv4Packet[2] = byte(totalLen >> 8)
	ipv4Packet[3] = byte(totalLen)

	// Identification (16 bits) - use a simple counter or random value
	ipv4Packet[4] = 0x00
	ipv4Packet[5] = 0x01

	// Flags and Fragment Offset (16 bits)
	ipv4Packet[6] = 0x40 // Don't Fragment flag
	ipv4Packet[7] = 0x00

	// TTL (same as IPv6 hop limit)
	ipv4Packet[8] = ipv6Hdr.HopLimit

	// Protocol (same as IPv6 next header)
	ipv4Packet[9] = ipv6Hdr.NextHdr

	// Header checksum (will be calculated later)
	ipv4Packet[10] = 0x00
	ipv4Packet[11] = 0x00

	// Source address (4 bytes)
	srcIPv4 := srcIP.To4()
	if srcIPv4 == nil {
		return nil, fmt.Errorf("invalid IPv4 source address")
	}
	copy(ipv4Packet[12:16], srcIPv4)

	// Destination address (4 bytes)
	dstIPv4 := dstIP.To4()
	if dstIPv4 == nil {
		return nil, fmt.Errorf("invalid IPv4 destination address")
	}
	copy(ipv4Packet[16:20], dstIPv4)

	// Copy payload from IPv6 packet
	copy(ipv4Packet[20:], ipv6Packet[40:])

	// Calculate and set IPv4 header checksum
	checksum := p.calculateIPv4Checksum(ipv4Packet[:20])
	ipv4Packet[10] = byte(checksum >> 8)
	ipv4Packet[11] = byte(checksum)

	return ipv4Packet, nil
}

// calculateIPv4Checksum calculates the IPv4 header checksum
func (p *Proxy) calculateIPv4Checksum(header []byte) uint16 {
	// Ensure header is even length
	if len(header)%2 != 0 {
		header = append(header, 0)
	}

	var sum uint32

	// Sum all 16-bit words
	for i := 0; i < len(header); i += 2 {
		word := uint32(header[i])<<8 + uint32(header[i+1])
		sum += word
	}

	// Add carry bits
	for sum>>16 != 0 {
		sum = (sum & 0xFFFF) + (sum >> 16)
	}

	// One's complement
	return uint16(^sum)
}
