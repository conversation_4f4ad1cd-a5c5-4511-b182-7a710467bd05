package config

import (
	"os"
	"testing"
)

func TestDefaultConfig(t *testing.T) {
	cfg := DefaultConfig()
	
	if cfg.SourceSubnet != "***********/24" {
		t.<PERSON><PERSON><PERSON>("Expected default source subnet '***********/24', got '%s'", cfg.SourceSubnet)
	}
	
	if cfg.TargetIPv6 != "fd7a:115c:a1e0:b1a:0:7:c0a8:601" {
		t.<PERSON><PERSON>("Expected default target IPv6 'fd7a:115c:a1e0:b1a:0:7:c0a8:601', got '%s'", cfg.TargetIPv6)
	}
	
	if cfg.LogLevel != "info" {
		t.<PERSON>rrorf("Expected default log level 'info', got '%s'", cfg.LogLevel)
	}
	
	if cfg.MaxConnections != 1000 {
		t.<PERSON><PERSON>rf("Expected default max connections 1000, got %d", cfg.MaxConnections)
	}
}

func TestConfigValidation(t *testing.T) {
	tests := []struct {
		name        string
		config      *Config
		expectError bool
	}{
		{
			name:        "Valid config",
			config:      DefaultConfig(),
			expectError: false,
		},
		{
			name: "Invalid source subnet",
			config: &Config{
				SourceSubnet:      "invalid-subnet",
				TargetIPv6:        "fd7a:115c:a1e0:b1a:0:7:c0a8:601",
				LogLevel:          "info",
				MaxConnections:    1000,
				ConnectionTimeout: 300,
				BufferSize:        65536,
				MetricsPort:       8080,
			},
			expectError: true,
		},
		{
			name: "Invalid IPv6 address",
			config: &Config{
				SourceSubnet:      "***********/24",
				TargetIPv6:        "***********", // IPv4 instead of IPv6
				LogLevel:          "info",
				MaxConnections:    1000,
				ConnectionTimeout: 300,
				BufferSize:        65536,
				MetricsPort:       8080,
			},
			expectError: true,
		},
		{
			name: "Invalid log level",
			config: &Config{
				SourceSubnet:      "***********/24",
				TargetIPv6:        "fd7a:115c:a1e0:b1a:0:7:c0a8:601",
				LogLevel:          "invalid",
				MaxConnections:    1000,
				ConnectionTimeout: 300,
				BufferSize:        65536,
				MetricsPort:       8080,
			},
			expectError: true,
		},
		{
			name: "Negative max connections",
			config: &Config{
				SourceSubnet:      "***********/24",
				TargetIPv6:        "fd7a:115c:a1e0:b1a:0:7:c0a8:601",
				LogLevel:          "info",
				MaxConnections:    -1,
				ConnectionTimeout: 300,
				BufferSize:        65536,
				MetricsPort:       8080,
			},
			expectError: true,
		},
		{
			name: "Zero connection timeout",
			config: &Config{
				SourceSubnet:      "***********/24",
				TargetIPv6:        "fd7a:115c:a1e0:b1a:0:7:c0a8:601",
				LogLevel:          "info",
				MaxConnections:    1000,
				ConnectionTimeout: 0,
				BufferSize:        65536,
				MetricsPort:       8080,
			},
			expectError: true,
		},
		{
			name: "Invalid metrics port",
			config: &Config{
				SourceSubnet:      "***********/24",
				TargetIPv6:        "fd7a:115c:a1e0:b1a:0:7:c0a8:601",
				LogLevel:          "info",
				MaxConnections:    1000,
				ConnectionTimeout: 300,
				BufferSize:        65536,
				MetricsPort:       70000, // Too high
			},
			expectError: true,
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.Validate()
			if tt.expectError && err == nil {
				t.Errorf("Expected error but got none")
			}
			if !tt.expectError && err != nil {
				t.Errorf("Expected no error but got: %v", err)
			}
		})
	}
}

func TestLoadFromEnv(t *testing.T) {
	// Set environment variables
	os.Setenv("GOPROXY_SOURCE_SUBNET", "10.0.0.0/24")
	os.Setenv("GOPROXY_TARGET_IPV6", "2001:db8::1")
	os.Setenv("GOPROXY_LOG_LEVEL", "debug")
	
	defer func() {
		os.Unsetenv("GOPROXY_SOURCE_SUBNET")
		os.Unsetenv("GOPROXY_TARGET_IPV6")
		os.Unsetenv("GOPROXY_LOG_LEVEL")
	}()
	
	cfg := DefaultConfig()
	cfg.loadFromEnv()
	
	if cfg.SourceSubnet != "10.0.0.0/24" {
		t.Errorf("Expected source subnet from env '10.0.0.0/24', got '%s'", cfg.SourceSubnet)
	}
	
	if cfg.TargetIPv6 != "2001:db8::1" {
		t.Errorf("Expected target IPv6 from env '2001:db8::1', got '%s'", cfg.TargetIPv6)
	}
	
	if cfg.LogLevel != "debug" {
		t.Errorf("Expected log level from env 'debug', got '%s'", cfg.LogLevel)
	}
}

func TestSaveToFile(t *testing.T) {
	cfg := DefaultConfig()
	cfg.SourceSubnet = "10.0.0.0/24"
	cfg.TargetIPv6 = "2001:db8::1"
	
	tempFile := "test_config.yaml"
	defer os.Remove(tempFile)
	
	err := cfg.SaveToFile(tempFile)
	if err != nil {
		t.Fatalf("Failed to save config to file: %v", err)
	}
	
	// Verify file exists
	if _, err := os.Stat(tempFile); os.IsNotExist(err) {
		t.Errorf("Config file was not created")
	}
	
	// Load the config back
	newCfg := DefaultConfig()
	err = newCfg.loadFromFile(tempFile)
	if err != nil {
		t.Fatalf("Failed to load config from file: %v", err)
	}
	
	if newCfg.SourceSubnet != cfg.SourceSubnet {
		t.Errorf("Source subnet mismatch after save/load: expected '%s', got '%s'", 
			cfg.SourceSubnet, newCfg.SourceSubnet)
	}
	
	if newCfg.TargetIPv6 != cfg.TargetIPv6 {
		t.Errorf("Target IPv6 mismatch after save/load: expected '%s', got '%s'", 
			cfg.TargetIPv6, newCfg.TargetIPv6)
	}
}
