package config

import (
	"flag"
	"fmt"
	"net"
	"os"
	"path/filepath"

	"gopkg.in/yaml.v3"
)

// Config holds all configuration options for the proxy
type Config struct {
	// SourceSubnet is the IPv4 subnet to intercept (e.g., "***********/24")
	SourceSubnet string `yaml:"source_subnet" json:"source_subnet"`

	// TargetIPv6 is the IPv6 address to forward traffic to
	TargetIPv6 string `yaml:"target_ipv6" json:"target_ipv6"`

	// LogLevel controls logging verbosity (debug, info, warn, error)
	LogLevel string `yaml:"log_level" json:"log_level"`

	// ConfigFile path to configuration file
	ConfigFile string `yaml:"-" json:"-"`

	// BindInterface specifies which network interface to bind to (optional)
	BindInterface string `yaml:"bind_interface" json:"bind_interface"`

	// MaxConnections limits concurrent connections (0 = unlimited)
	MaxConnections int `yaml:"max_connections" json:"max_connections"`

	// ConnectionTimeout in seconds for idle connections
	ConnectionTimeout int `yaml:"connection_timeout" json:"connection_timeout"`

	// BufferSize for packet processing
	BufferSize int `yaml:"buffer_size" json:"buffer_size"`

	// EnableMetrics enables prometheus metrics endpoint
	EnableMetrics bool `yaml:"enable_metrics" json:"enable_metrics"`

	// MetricsPort for metrics HTTP server
	MetricsPort int `yaml:"metrics_port" json:"metrics_port"`
}

// DefaultConfig returns a configuration with sensible defaults
func DefaultConfig() *Config {
	return &Config{
		SourceSubnet:      "***********/24",
		TargetIPv6:        "fd7a:115c:a1e0:b1a:0:7:c0a8:601",
		LogLevel:          "info",
		ConfigFile:        "",
		BindInterface:     "",
		MaxConnections:    1000,
		ConnectionTimeout: 300, // 5 minutes
		BufferSize:        65536,
		EnableMetrics:     false,
		MetricsPort:       8080,
	}
}

// LoadConfig loads configuration from file, environment variables, and command line flags
func LoadConfig() (*Config, error) {
	config := DefaultConfig()

	// Parse command line flags first to get config file path
	flag.StringVar(&config.ConfigFile, "config", "", "Path to configuration file")
	flag.StringVar(&config.SourceSubnet, "subnet", config.SourceSubnet, "Source IPv4 subnet to intercept")
	flag.StringVar(&config.TargetIPv6, "target", config.TargetIPv6, "Target IPv6 address")
	flag.StringVar(&config.LogLevel, "log-level", config.LogLevel, "Log level (debug, info, warn, error)")
	flag.StringVar(&config.BindInterface, "interface", config.BindInterface, "Network interface to bind to")
	flag.IntVar(&config.MaxConnections, "max-connections", config.MaxConnections, "Maximum concurrent connections")
	flag.IntVar(&config.ConnectionTimeout, "timeout", config.ConnectionTimeout, "Connection timeout in seconds")
	flag.IntVar(&config.BufferSize, "buffer-size", config.BufferSize, "Buffer size for packet processing")
	flag.BoolVar(&config.EnableMetrics, "metrics", config.EnableMetrics, "Enable metrics endpoint")
	flag.IntVar(&config.MetricsPort, "metrics-port", config.MetricsPort, "Metrics HTTP server port")

	flag.Parse()

	// Load from config file if specified
	if config.ConfigFile != "" {
		if err := config.loadFromFile(config.ConfigFile); err != nil {
			return nil, fmt.Errorf("failed to load config file: %w", err)
		}
	} else {
		// Try to load from default locations
		defaultPaths := []string{
			"config.yaml",
			"config.yml",
			filepath.Join(os.Getenv("HOME"), ".goproxy", "config.yaml"),
			"/etc/goproxy/config.yaml",
		}

		for _, path := range defaultPaths {
			if _, err := os.Stat(path); err == nil {
				if err := config.loadFromFile(path); err == nil {
					break
				}
			}
		}
	}

	// Load from environment variables
	config.loadFromEnv()

	// Validate configuration
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("invalid configuration: %w", err)
	}

	return config, nil
}

// loadFromFile loads configuration from a YAML file
func (c *Config) loadFromFile(filename string) error {
	data, err := os.ReadFile(filename)
	if err != nil {
		return err
	}

	return yaml.Unmarshal(data, c)
}

// loadFromEnv loads configuration from environment variables
func (c *Config) loadFromEnv() {
	if subnet := os.Getenv("GOPROXY_SOURCE_SUBNET"); subnet != "" {
		c.SourceSubnet = subnet
	}
	if target := os.Getenv("GOPROXY_TARGET_IPV6"); target != "" {
		c.TargetIPv6 = target
	}
	if logLevel := os.Getenv("GOPROXY_LOG_LEVEL"); logLevel != "" {
		c.LogLevel = logLevel
	}
	if iface := os.Getenv("GOPROXY_BIND_INTERFACE"); iface != "" {
		c.BindInterface = iface
	}
}

// Validate checks if the configuration is valid
func (c *Config) Validate() error {
	// Validate source subnet
	_, _, err := net.ParseCIDR(c.SourceSubnet)
	if err != nil {
		return fmt.Errorf("invalid source subnet %q: %w", c.SourceSubnet, err)
	}

	// Validate target IPv6 address
	ip := net.ParseIP(c.TargetIPv6)
	if ip == nil || ip.To4() != nil {
		return fmt.Errorf("invalid IPv6 address %q", c.TargetIPv6)
	}

	// Validate log level
	validLogLevels := map[string]bool{
		"debug": true,
		"info":  true,
		"warn":  true,
		"error": true,
	}
	if !validLogLevels[c.LogLevel] {
		return fmt.Errorf("invalid log level %q", c.LogLevel)
	}

	// Validate numeric values
	if c.MaxConnections < 0 {
		return fmt.Errorf("max_connections must be >= 0")
	}
	if c.ConnectionTimeout <= 0 {
		return fmt.Errorf("connection_timeout must be > 0")
	}
	if c.BufferSize <= 0 {
		return fmt.Errorf("buffer_size must be > 0")
	}
	if c.MetricsPort <= 0 || c.MetricsPort > 65535 {
		return fmt.Errorf("metrics_port must be between 1 and 65535")
	}

	return nil
}

// SaveToFile saves the current configuration to a YAML file
func (c *Config) SaveToFile(filename string) error {
	data, err := yaml.Marshal(c)
	if err != nil {
		return err
	}

	return os.WriteFile(filename, data, 0644)
}
