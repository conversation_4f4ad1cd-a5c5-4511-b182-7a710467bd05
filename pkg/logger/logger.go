package logger

import (
	"fmt"
	"io"
	"log"
	"os"
	"strings"
	"time"
)

// LogLevel represents the logging level
type Log<PERSON>evel int

const (
	DEBUG LogLevel = iota
	INFO
	WARN
	ERROR
)

// String returns the string representation of the log level
func (l LogLevel) String() string {
	switch l {
	case DEBUG:
		return "DEBUG"
	case INFO:
		return "INFO"
	case WARN:
		return "WARN"
	case ERROR:
		return "ERROR"
	default:
		return "UNKNOWN"
	}
}

// ParseLogLevel parses a string into a LogLevel
func ParseLogLevel(level string) LogLevel {
	switch strings.ToLower(level) {
	case "debug":
		return DEBUG
	case "info":
		return INFO
	case "warn", "warning":
		return WARN
	case "error":
		return ERROR
	default:
		return INFO
	}
}

// Logger provides structured logging functionality
type Logger struct {
	level  LogLevel
	output io.Writer
	logger *log.Logger
}

// New creates a new logger with the specified level and output
func New(level LogLevel, output io.Writer) *Logger {
	if output == nil {
		output = os.Stdout
	}

	return &Logger{
		level:  level,
		output: output,
		logger: log.New(output, "", 0),
	}
}

// NewFromString creates a new logger from a string level
func NewFromString(level string, output io.Writer) *Logger {
	return New(ParseLogLevel(level), output)
}

// SetLevel sets the logging level
func (l *Logger) SetLevel(level LogLevel) {
	l.level = level
}

// SetLevelFromString sets the logging level from a string
func (l *Logger) SetLevelFromString(level string) {
	l.level = ParseLogLevel(level)
}

// GetLevel returns the current logging level
func (l *Logger) GetLevel() LogLevel {
	return l.level
}

// log writes a log message if the level is appropriate
func (l *Logger) log(level LogLevel, format string, args ...interface{}) {
	if level < l.level {
		return
	}

	timestamp := time.Now().Format("2006-01-02 15:04:05.000")
	message := fmt.Sprintf(format, args...)
	
	logLine := fmt.Sprintf("[%s] %s: %s", timestamp, level.String(), message)
	l.logger.Println(logLine)
}

// Debug logs a debug message
func (l *Logger) Debug(format string, args ...interface{}) {
	l.log(DEBUG, format, args...)
}

// Info logs an info message
func (l *Logger) Info(format string, args ...interface{}) {
	l.log(INFO, format, args...)
}

// Warn logs a warning message
func (l *Logger) Warn(format string, args ...interface{}) {
	l.log(WARN, format, args...)
}

// Error logs an error message
func (l *Logger) Error(format string, args ...interface{}) {
	l.log(ERROR, format, args...)
}

// Fatal logs an error message and exits the program
func (l *Logger) Fatal(format string, args ...interface{}) {
	l.log(ERROR, format, args...)
	os.Exit(1)
}

// WithField returns a new logger with an additional field
func (l *Logger) WithField(key, value string) *FieldLogger {
	return &FieldLogger{
		logger: l,
		fields: map[string]string{key: value},
	}
}

// WithFields returns a new logger with additional fields
func (l *Logger) WithFields(fields map[string]string) *FieldLogger {
	return &FieldLogger{
		logger: l,
		fields: fields,
	}
}

// FieldLogger provides logging with structured fields
type FieldLogger struct {
	logger *Logger
	fields map[string]string
}

// formatFields formats the fields for logging
func (fl *FieldLogger) formatFields() string {
	if len(fl.fields) == 0 {
		return ""
	}

	var parts []string
	for key, value := range fl.fields {
		parts = append(parts, fmt.Sprintf("%s=%s", key, value))
	}
	return " [" + strings.Join(parts, " ") + "]"
}

// log writes a log message with fields
func (fl *FieldLogger) log(level LogLevel, format string, args ...interface{}) {
	if level < fl.logger.level {
		return
	}

	timestamp := time.Now().Format("2006-01-02 15:04:05.000")
	message := fmt.Sprintf(format, args...)
	fields := fl.formatFields()
	
	logLine := fmt.Sprintf("[%s] %s: %s%s", timestamp, level.String(), message, fields)
	fl.logger.logger.Println(logLine)
}

// Debug logs a debug message with fields
func (fl *FieldLogger) Debug(format string, args ...interface{}) {
	fl.log(DEBUG, format, args...)
}

// Info logs an info message with fields
func (fl *FieldLogger) Info(format string, args ...interface{}) {
	fl.log(INFO, format, args...)
}

// Warn logs a warning message with fields
func (fl *FieldLogger) Warn(format string, args ...interface{}) {
	fl.log(WARN, format, args...)
}

// Error logs an error message with fields
func (fl *FieldLogger) Error(format string, args ...interface{}) {
	fl.log(ERROR, format, args...)
}

// Fatal logs an error message with fields and exits the program
func (fl *FieldLogger) Fatal(format string, args ...interface{}) {
	fl.log(ERROR, format, args...)
	os.Exit(1)
}

// WithField adds another field to the logger
func (fl *FieldLogger) WithField(key, value string) *FieldLogger {
	newFields := make(map[string]string)
	for k, v := range fl.fields {
		newFields[k] = v
	}
	newFields[key] = value

	return &FieldLogger{
		logger: fl.logger,
		fields: newFields,
	}
}

// WithFields adds multiple fields to the logger
func (fl *FieldLogger) WithFields(fields map[string]string) *FieldLogger {
	newFields := make(map[string]string)
	for k, v := range fl.fields {
		newFields[k] = v
	}
	for k, v := range fields {
		newFields[k] = v
	}

	return &FieldLogger{
		logger: fl.logger,
		fields: newFields,
	}
}

// Default logger instance
var defaultLogger = New(INFO, os.Stdout)

// SetDefaultLevel sets the level for the default logger
func SetDefaultLevel(level LogLevel) {
	defaultLogger.SetLevel(level)
}

// SetDefaultLevelFromString sets the level for the default logger from a string
func SetDefaultLevelFromString(level string) {
	defaultLogger.SetLevelFromString(level)
}

// Package-level logging functions using the default logger
func Debug(format string, args ...interface{}) {
	defaultLogger.Debug(format, args...)
}

func Info(format string, args ...interface{}) {
	defaultLogger.Info(format, args...)
}

func Warn(format string, args ...interface{}) {
	defaultLogger.Warn(format, args...)
}

func Error(format string, args ...interface{}) {
	defaultLogger.Error(format, args...)
}

func Fatal(format string, args ...interface{}) {
	defaultLogger.Fatal(format, args...)
}

func WithField(key, value string) *FieldLogger {
	return defaultLogger.WithField(key, value)
}

func WithFields(fields map[string]string) *FieldLogger {
	return defaultLogger.WithFields(fields)
}
