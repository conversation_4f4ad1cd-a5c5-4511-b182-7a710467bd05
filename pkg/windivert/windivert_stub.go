//go:build !windows
// +build !windows

package windivert

import (
	"fmt"
	"net"
)

// WinDivert constants (stub values)
const (
	WINDIVERT_LAYER_NETWORK         = 0
	WINDIVERT_LAYER_NETWORK_FORWARD = 1

	WINDIVERT_FLAG_SNIFF      = 1
	WINDIVERT_FLAG_DROP       = 2
	WINDIVERT_FLAG_RECV_ONLY  = 4
	WINDIVERT_FLAG_SEND_ONLY  = 8
	WINDIVERT_FLAG_NO_INSTALL = 16
	WINDIVERT_FLAG_FRAGMENTS  = 32
)

// WinDivertAddress represents packet metadata (stub)
type WinDivertAddress struct {
	Timestamp   int64
	Layer       uint8
	Event       uint8
	Sniffed     uint8
	Outbound    uint8
	Loopback    uint8
	Impostor    uint8
	IPv6        uint8
	IPChecksum  uint8
	TCPChecksum uint8
	UDPChecksum uint8
	Reserved1   uint8
	Reserved2   uint32
	IfIdx       uint32
	SubIfIdx    uint32
}

// Handle represents a WinDivert handle (stub)
type Handle struct {
	handle uintptr
}

// LoadWinDivert loads the WinDivert library (stub)
func LoadWinDivert() (interface{}, error) {
	return nil, fmt.Errorf("WinDivert is only supported on Windows")
}

// Open opens a WinDivert handle (stub)
func Open(filter string, layer uint8, priority int16, flags uint64) (*Handle, error) {
	return nil, fmt.Errorf("WinDivert is only supported on Windows")
}

// Close closes the WinDivert handle (stub)
func (h *Handle) Close() error {
	return fmt.Errorf("WinDivert is only supported on Windows")
}

// Recv receives a packet from the WinDivert handle (stub)
func (h *Handle) Recv(packet []byte) (int, *WinDivertAddress, error) {
	return 0, nil, fmt.Errorf("WinDivert is only supported on Windows")
}

// Send sends a packet through the WinDivert handle (stub)
func (h *Handle) Send(packet []byte, addr *WinDivertAddress) (int, error) {
	return 0, fmt.Errorf("WinDivert is only supported on Windows")
}

// SetParam sets a parameter on the WinDivert handle (stub)
func (h *Handle) SetParam(param uint32, value uint64) error {
	return fmt.Errorf("WinDivert is only supported on Windows")
}

// GetParam gets a parameter from the WinDivert handle (stub)
func (h *Handle) GetParam(param uint32) (uint64, error) {
	return 0, fmt.Errorf("WinDivert is only supported on Windows")
}

// HelperCalcChecksums calculates checksums for a packet (stub)
func HelperCalcChecksums(packet []byte, addr *WinDivertAddress, flags uint64) error {
	return fmt.Errorf("WinDivert is only supported on Windows")
}

// HelperParsePacket parses packet headers (stub)
func HelperParsePacket(packet []byte) (*IPv4Header, *IPv6Header, *TCPHeader, *UDPHeader, error) {
	return nil, nil, nil, nil, fmt.Errorf("WinDivert is only supported on Windows")
}

// IPv4Header represents an IPv4 header (stub)
type IPv4Header struct {
	HdrLength uint8
	Version   uint8
	TOS       uint8
	Length    uint16
	Id        uint16
	FragOff0  uint16
	TTL       uint8
	Protocol  uint8
	Checksum  uint16
	SrcAddr   uint32
	DstAddr   uint32
}

// IPv6Header represents an IPv6 header (stub)
type IPv6Header struct {
	TrafficClass0 uint8
	Version       uint8
	TrafficClass1 uint8
	FlowLabel0    uint8
	FlowLabel1    uint16
	Length        uint16
	NextHdr       uint8
	HopLimit      uint8
	SrcAddr       [16]byte
	DstAddr       [16]byte
}

// TCPHeader represents a TCP header (stub)
type TCPHeader struct {
	SrcPort   uint16
	DstPort   uint16
	SeqNum    uint32
	AckNum    uint32
	Reserved  uint8
	HdrLength uint8
	Flags     uint8
	Window    uint16
	Checksum  uint16
	UrgPtr    uint16
}

// UDPHeader represents a UDP header (stub)
type UDPHeader struct {
	SrcPort  uint16
	DstPort  uint16
	Length   uint16
	Checksum uint16
}

// GetSrcIP returns the source IP address from an IPv4 header (stub)
func (h *IPv4Header) GetSrcIP() net.IP {
	return net.IPv4(
		byte(h.SrcAddr),
		byte(h.SrcAddr>>8),
		byte(h.SrcAddr>>16),
		byte(h.SrcAddr>>24),
	)
}

// GetDstIP returns the destination IP address from an IPv4 header (stub)
func (h *IPv4Header) GetDstIP() net.IP {
	return net.IPv4(
		byte(h.DstAddr),
		byte(h.DstAddr>>8),
		byte(h.DstAddr>>16),
		byte(h.DstAddr>>24),
	)
}

// SetSrcIP sets the source IP address in an IPv4 header (stub)
func (h *IPv4Header) SetSrcIP(ip net.IP) {
	ip = ip.To4()
	if ip != nil {
		h.SrcAddr = uint32(ip[0]) | uint32(ip[1])<<8 | uint32(ip[2])<<16 | uint32(ip[3])<<24
	}
}

// SetDstIP sets the destination IP address in an IPv4 header (stub)
func (h *IPv4Header) SetDstIP(ip net.IP) {
	ip = ip.To4()
	if ip != nil {
		h.DstAddr = uint32(ip[0]) | uint32(ip[1])<<8 | uint32(ip[2])<<16 | uint32(ip[3])<<24
	}
}

// GetSrcIP returns the source IP address from an IPv6 header (stub)
func (h *IPv6Header) GetSrcIP() net.IP {
	return net.IP(h.SrcAddr[:])
}

// GetDstIP returns the destination IP address from an IPv6 header (stub)
func (h *IPv6Header) GetDstIP() net.IP {
	return net.IP(h.DstAddr[:])
}

// SetSrcIP sets the source IP address in an IPv6 header (stub)
func (h *IPv6Header) SetSrcIP(ip net.IP) {
	ip = ip.To16()
	if ip != nil {
		copy(h.SrcAddr[:], ip)
	}
}

// SetDstIP sets the destination IP address in an IPv6 header (stub)
func (h *IPv6Header) SetDstIP(ip net.IP) {
	ip = ip.To16()
	if ip != nil {
		copy(h.DstAddr[:], ip)
	}
}
