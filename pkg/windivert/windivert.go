// +build windows

package windivert

import (
	"fmt"
	"net"
	"syscall"
	"unsafe"
)

// WinDivert constants
const (
	WINDIVERT_LAYER_NETWORK         = 0
	WINDIVERT_LAYER_NETWORK_FORWARD = 1
	
	WINDIVERT_FLAG_SNIFF    = 1
	WINDIVERT_FLAG_DROP     = 2
	WINDIVERT_FLAG_RECV_ONLY = 4
	WINDIVERT_FLAG_SEND_ONLY = 8
	WINDIVERT_FLAG_NO_INSTALL = 16
	WINDIVERT_FLAG_FRAGMENTS = 32
)

// WinDivertAddress represents packet metadata
type WinDivertAddress struct {
	Timestamp     int64
	Layer         uint8
	Event         uint8
	Sniffed       uint8
	Outbound      uint8
	Loopback      uint8
	Impostor      uint8
	IPv6          uint8
	IPChecksum    uint8
	TCPChecksum   uint8
	UDPChecksum   uint8
	Reserved1     uint8
	Reserved2     uint32
	IfIdx         uint32
	SubIfIdx      uint32
}

// Handle represents a WinDivert handle
type Handle struct {
	handle syscall.Handle
	dll    *syscall.LazyDLL
}

// LoadWinDivert loads the WinDivert library
func LoadWinDivert() (*syscall.LazyDLL, error) {
	// Try to load WinDivert.dll from current directory first
	dll := syscall.NewLazyDLL("WinDivert.dll")
	if err := dll.Load(); err != nil {
		// Try system path
		dll = syscall.NewLazyDLL("WinDivert")
		if err := dll.Load(); err != nil {
			return nil, fmt.Errorf("failed to load WinDivert.dll: %w", err)
		}
	}
	return dll, nil
}

// Open opens a WinDivert handle
func Open(filter string, layer uint8, priority int16, flags uint64) (*Handle, error) {
	dll, err := LoadWinDivert()
	if err != nil {
		return nil, err
	}

	proc := dll.NewProc("WinDivertOpen")
	filterPtr, err := syscall.UTF16PtrFromString(filter)
	if err != nil {
		return nil, fmt.Errorf("failed to convert filter string: %w", err)
	}

	ret, _, errno := proc.Call(
		uintptr(unsafe.Pointer(filterPtr)),
		uintptr(layer),
		uintptr(priority),
		uintptr(flags),
	)

	if ret == uintptr(syscall.InvalidHandle) {
		return nil, fmt.Errorf("WinDivertOpen failed: %w", errno)
	}

	return &Handle{
		handle: syscall.Handle(ret),
		dll:    dll,
	}, nil
}

// Close closes the WinDivert handle
func (h *Handle) Close() error {
	if h.handle == syscall.InvalidHandle {
		return nil
	}

	proc := h.dll.NewProc("WinDivertClose")
	ret, _, errno := proc.Call(uintptr(h.handle))
	
	h.handle = syscall.InvalidHandle
	
	if ret == 0 {
		return fmt.Errorf("WinDivertClose failed: %w", errno)
	}
	
	return nil
}

// Recv receives a packet from the WinDivert handle
func (h *Handle) Recv(packet []byte) (int, *WinDivertAddress, error) {
	if h.handle == syscall.InvalidHandle {
		return 0, nil, fmt.Errorf("handle is closed")
	}

	proc := h.dll.NewProc("WinDivertRecv")
	addr := &WinDivertAddress{}
	var recvLen uint32

	ret, _, errno := proc.Call(
		uintptr(h.handle),
		uintptr(unsafe.Pointer(&packet[0])),
		uintptr(len(packet)),
		uintptr(unsafe.Pointer(&recvLen)),
		uintptr(unsafe.Pointer(addr)),
	)

	if ret == 0 {
		return 0, nil, fmt.Errorf("WinDivertRecv failed: %w", errno)
	}

	return int(recvLen), addr, nil
}

// Send sends a packet through the WinDivert handle
func (h *Handle) Send(packet []byte, addr *WinDivertAddress) (int, error) {
	if h.handle == syscall.InvalidHandle {
		return 0, fmt.Errorf("handle is closed")
	}

	proc := h.dll.NewProc("WinDivertSend")
	var sendLen uint32

	ret, _, errno := proc.Call(
		uintptr(h.handle),
		uintptr(unsafe.Pointer(&packet[0])),
		uintptr(len(packet)),
		uintptr(unsafe.Pointer(&sendLen)),
		uintptr(unsafe.Pointer(addr)),
	)

	if ret == 0 {
		return 0, fmt.Errorf("WinDivertSend failed: %w", errno)
	}

	return int(sendLen), nil
}

// SetParam sets a parameter on the WinDivert handle
func (h *Handle) SetParam(param uint32, value uint64) error {
	if h.handle == syscall.InvalidHandle {
		return fmt.Errorf("handle is closed")
	}

	proc := h.dll.NewProc("WinDivertSetParam")
	ret, _, errno := proc.Call(
		uintptr(h.handle),
		uintptr(param),
		uintptr(value),
	)

	if ret == 0 {
		return fmt.Errorf("WinDivertSetParam failed: %w", errno)
	}

	return nil
}

// GetParam gets a parameter from the WinDivert handle
func (h *Handle) GetParam(param uint32) (uint64, error) {
	if h.handle == syscall.InvalidHandle {
		return 0, fmt.Errorf("handle is closed")
	}

	proc := h.dll.NewProc("WinDivertGetParam")
	var value uint64

	ret, _, errno := proc.Call(
		uintptr(h.handle),
		uintptr(param),
		uintptr(unsafe.Pointer(&value)),
	)

	if ret == 0 {
		return 0, fmt.Errorf("WinDivertGetParam failed: %w", errno)
	}

	return value, nil
}

// HelperCalcChecksums calculates checksums for a packet
func HelperCalcChecksums(packet []byte, addr *WinDivertAddress, flags uint64) error {
	dll, err := LoadWinDivert()
	if err != nil {
		return err
	}

	proc := dll.NewProc("WinDivertHelperCalcChecksums")
	ret, _, errno := proc.Call(
		uintptr(unsafe.Pointer(&packet[0])),
		uintptr(len(packet)),
		uintptr(unsafe.Pointer(addr)),
		uintptr(flags),
	)

	if ret == 0 {
		return fmt.Errorf("WinDivertHelperCalcChecksums failed: %w", errno)
	}

	return nil
}

// HelperParsePacket parses packet headers
func HelperParsePacket(packet []byte) (*IPv4Header, *IPv6Header, *TCPHeader, *UDPHeader, error) {
	dll, err := LoadWinDivert()
	if err != nil {
		return nil, nil, nil, nil, err
	}

	proc := dll.NewProc("WinDivertHelperParsePacket")
	
	var ipv4Hdr *IPv4Header
	var ipv6Hdr *IPv6Header
	var tcpHdr *TCPHeader
	var udpHdr *UDPHeader
	var icmpHdr unsafe.Pointer
	var icmpv6Hdr unsafe.Pointer
	var payload unsafe.Pointer
	var payloadLen uint32

	ret, _, errno := proc.Call(
		uintptr(unsafe.Pointer(&packet[0])),
		uintptr(len(packet)),
		uintptr(unsafe.Pointer(&ipv4Hdr)),
		uintptr(unsafe.Pointer(&ipv6Hdr)),
		uintptr(unsafe.Pointer(&icmpHdr)),
		uintptr(unsafe.Pointer(&icmpv6Hdr)),
		uintptr(unsafe.Pointer(&tcpHdr)),
		uintptr(unsafe.Pointer(&udpHdr)),
		uintptr(unsafe.Pointer(&payload)),
		uintptr(unsafe.Pointer(&payloadLen)),
	)

	if ret == 0 {
		return nil, nil, nil, nil, fmt.Errorf("WinDivertHelperParsePacket failed: %w", errno)
	}

	return ipv4Hdr, ipv6Hdr, tcpHdr, udpHdr, nil
}

// IPv4Header represents an IPv4 header
type IPv4Header struct {
	HdrLength uint8
	Version   uint8
	TOS       uint8
	Length    uint16
	Id        uint16
	FragOff0  uint16
	TTL       uint8
	Protocol  uint8
	Checksum  uint16
	SrcAddr   uint32
	DstAddr   uint32
}

// IPv6Header represents an IPv6 header
type IPv6Header struct {
	TrafficClass0 uint8
	Version       uint8
	TrafficClass1 uint8
	FlowLabel0    uint8
	FlowLabel1    uint16
	Length        uint16
	NextHdr       uint8
	HopLimit      uint8
	SrcAddr       [16]byte
	DstAddr       [16]byte
}

// TCPHeader represents a TCP header
type TCPHeader struct {
	SrcPort  uint16
	DstPort  uint16
	SeqNum   uint32
	AckNum   uint32
	Reserved uint8
	HdrLength uint8
	Flags    uint8
	Window   uint16
	Checksum uint16
	UrgPtr   uint16
}

// UDPHeader represents a UDP header
type UDPHeader struct {
	SrcPort  uint16
	DstPort  uint16
	Length   uint16
	Checksum uint16
}

// GetSrcIP returns the source IP address from an IPv4 header
func (h *IPv4Header) GetSrcIP() net.IP {
	return net.IPv4(
		byte(h.SrcAddr),
		byte(h.SrcAddr>>8),
		byte(h.SrcAddr>>16),
		byte(h.SrcAddr>>24),
	)
}

// GetDstIP returns the destination IP address from an IPv4 header
func (h *IPv4Header) GetDstIP() net.IP {
	return net.IPv4(
		byte(h.DstAddr),
		byte(h.DstAddr>>8),
		byte(h.DstAddr>>16),
		byte(h.DstAddr>>24),
	)
}

// SetSrcIP sets the source IP address in an IPv4 header
func (h *IPv4Header) SetSrcIP(ip net.IP) {
	ip = ip.To4()
	if ip != nil {
		h.SrcAddr = uint32(ip[0]) | uint32(ip[1])<<8 | uint32(ip[2])<<16 | uint32(ip[3])<<24
	}
}

// SetDstIP sets the destination IP address in an IPv4 header
func (h *IPv4Header) SetDstIP(ip net.IP) {
	ip = ip.To4()
	if ip != nil {
		h.DstAddr = uint32(ip[0]) | uint32(ip[1])<<8 | uint32(ip[2])<<16 | uint32(ip[3])<<24
	}
}

// GetSrcIP returns the source IP address from an IPv6 header
func (h *IPv6Header) GetSrcIP() net.IP {
	return net.IP(h.SrcAddr[:])
}

// GetDstIP returns the destination IP address from an IPv6 header
func (h *IPv6Header) GetDstIP() net.IP {
	return net.IP(h.DstAddr[:])
}

// SetSrcIP sets the source IP address in an IPv6 header
func (h *IPv6Header) SetSrcIP(ip net.IP) {
	ip = ip.To16()
	if ip != nil {
		copy(h.SrcAddr[:], ip)
	}
}

// SetDstIP sets the destination IP address in an IPv6 header
func (h *IPv6Header) SetDstIP(ip net.IP) {
	ip = ip.To16()
	if ip != nil {
		copy(h.DstAddr[:], ip)
	}
}
