# Troubleshooting Guide

## Common Issues and Solutions

### 1. Application Won't Start

#### Error: "Access denied" or "Permission denied"
**Cause:** Application not running with Administrator privileges.

**Solution:**
1. Right-click on Command Prompt or PowerShell
2. Select "Run as administrator"
3. Navigate to the GoProxy directory
4. Run the application

#### Error: "WinDivert.dll not found"
**Cause:** WinDivert driver files are missing or corrupted.

**Solution:**
1. Re-download the application
2. Ensure antivirus hasn't quarantined files
3. Add exception for GoProxy in antivirus software

#### Error: "Failed to load WinDivert.dll"
**Cause:** System incompatibility or driver signing issues.

**Solution:**
1. Verify Windows 10/11 (64-bit)
2. Check Windows Update status
3. Disable Secure Boot temporarily for testing
4. Run Windows System File Checker: `sfc /scannow`

### 2. Network Connectivity Issues

#### No packets being intercepted
**Symptoms:** Debug logs show no packet activity.

**Diagnosis:**
```cmd
goproxy.exe -log-level debug
```

**Solutions:**
1. **Check subnet configuration:**
   - Verify the source subnet matches your target network
   - Use `ipconfig` to check local network configuration

2. **Verify routing:**
   ```cmd
   route print
   tracert ***********
   ```

3. **Test with ping:**
   ```cmd
   ping *************
   ```

#### Packets intercepted but not forwarded
**Symptoms:** Debug logs show interception but forwarding fails.

**Solutions:**
1. **Check IPv6 connectivity:**
   ```cmd
   ping -6 fd7a:115c:a1e0:b1a:0:7:c0a8:601
   ```

2. **Verify target IPv6 address:**
   - Ensure the IPv6 address is correct
   - Check Tailscale status: `tailscale status`

3. **Check firewall rules:**
   - Windows Firewall may block IPv6 traffic
   - Add exception for GoProxy

### 3. Performance Issues

#### High CPU usage
**Causes:** Large packet volumes, inefficient processing.

**Solutions:**
1. **Optimize buffer size:**
   ```yaml
   buffer_size: 32768  # Reduce from default 65536
   ```

2. **Limit connections:**
   ```yaml
   max_connections: 500  # Reduce from default 1000
   ```

3. **Increase timeout:**
   ```yaml
   connection_timeout: 600  # Increase from default 300
   ```

#### High memory usage
**Causes:** Connection tracking overhead, memory leaks.

**Solutions:**
1. **Reduce connection timeout:**
   ```yaml
   connection_timeout: 60  # Faster cleanup
   ```

2. **Monitor connection count:**
   ```cmd
   goproxy.exe -metrics -metrics-port 8080
   # Check http://localhost:8080/metrics
   ```

3. **Restart periodically for long-running instances**

### 4. Connection Issues

#### Connections timing out
**Symptoms:** Applications report connection timeouts.

**Solutions:**
1. **Check connection tracking:**
   - Enable debug logging
   - Look for connection state messages

2. **Verify protocol support:**
   - GoProxy supports TCP and UDP
   - Some protocols may need special handling

3. **Test with simple protocols first:**
   ```cmd
   telnet ************* 22  # SSH
   telnet ************* 80  # HTTP
   ```

#### Partial connectivity
**Symptoms:** Some services work, others don't.

**Solutions:**
1. **Check port-specific issues:**
   - Some applications use non-standard ports
   - Verify target services are running

2. **Protocol-specific debugging:**
   ```cmd
   netstat -an | findstr 192.168.1
   ```

### 5. Logging and Debugging

#### Enable detailed logging
```cmd
goproxy.exe -log-level debug
```

#### Log analysis
Look for these patterns:

**Normal operation:**
```
[2024-08-22 10:30:05.123] DEBUG: Translating outbound packet: ********:12345 -> *************:80
[2024-08-22 10:30:05.124] DEBUG: Translating inbound packet: fd7a:115c:a1e0:b1a:0:7:c0a8:601:80 -> ********:12345
```

**Connection tracking:**
```
[2024-08-22 10:30:05.125] DEBUG: Tracking connection: tcp:********:12345->*************:80
```

**Errors to investigate:**
```
[2024-08-22 10:30:05.126] ERROR: Failed to receive outbound packet: ...
[2024-08-22 10:30:05.127] ERROR: Failed to translate IPv4 address: ...
```

### 6. System-Specific Issues

#### Windows Defender SmartScreen
**Symptoms:** Application blocked on first run.

**Solution:**
1. Click "More info" when prompted
2. Click "Run anyway"
3. Add permanent exception in Windows Defender

#### Antivirus Software
**Symptoms:** Application crashes or driver fails to load.

**Solution:**
1. Add GoProxy to antivirus exclusions
2. Temporarily disable real-time protection for testing
3. Check antivirus logs for blocked actions

#### Virtual Machines
**Symptoms:** Packet capture doesn't work in VM.

**Solution:**
1. Enable promiscuous mode on VM network adapter
2. Use bridged networking instead of NAT
3. Some hypervisors don't support packet injection

### 7. Network Configuration Issues

#### Multiple network adapters
**Symptoms:** Packets captured on wrong interface.

**Solution:**
```cmd
# List network interfaces
ipconfig /all

# Bind to specific interface
goproxy.exe -interface "Ethernet 2"
```

#### VPN interference
**Symptoms:** Proxy doesn't work with VPN active.

**Solution:**
1. Start GoProxy before connecting VPN
2. Configure VPN to exclude target subnet
3. Use split tunneling if available

### 8. Advanced Debugging

#### Packet capture analysis
Use Wireshark alongside GoProxy:

1. **Capture on local interface:**
   - Filter: `ip.dst_host in {***********/24}`
   - Look for IPv4 packets being intercepted

2. **Capture on Tailscale interface:**
   - Filter: `ipv6.dst_host == fd7a:115c:a1e0:b1a:0:7:c0a8:601`
   - Verify IPv6 packets are being sent

#### Registry and system state
Check for conflicting software:

```cmd
# List network drivers
driverquery | findstr /i network

# Check for other packet capture software
tasklist | findstr /i "wireshark\|npcap\|winpcap"
```

### 9. Getting Help

#### Information to collect
When reporting issues, include:

1. **System information:**
   ```cmd
   systeminfo | findstr /B /C:"OS Name" /C:"OS Version" /C:"System Type"
   ```

2. **Network configuration:**
   ```cmd
   ipconfig /all > network-config.txt
   route print > routing-table.txt
   ```

3. **GoProxy logs:**
   ```cmd
   goproxy.exe -log-level debug > goproxy-debug.log 2>&1
   ```

4. **Error messages and steps to reproduce**

#### Support channels
1. GitHub Issues (preferred)
2. Include all collected information
3. Describe expected vs actual behavior
4. Provide minimal reproduction steps

### 10. Recovery Procedures

#### Clean restart
1. Stop GoProxy (Ctrl+C)
2. Wait 10 seconds for cleanup
3. Restart with debug logging
4. Monitor for recurring issues

#### System cleanup
If issues persist:

1. **Restart network stack:**
   ```cmd
   netsh winsock reset
   netsh int ip reset
   # Restart computer
   ```

2. **Clear DNS cache:**
   ```cmd
   ipconfig /flushdns
   ```

3. **Reset network adapters:**
   ```cmd
   netsh int ip reset resetlog.txt
   ```
