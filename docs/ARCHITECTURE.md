# Architecture Overview

## System Design

GoProxy is a transparent IPv4 to IPv6 proxy designed specifically for Windows systems. It operates at the network layer to intercept IPv4 packets destined for a specific subnet and forwards them to an IPv6 address.

## Core Components

### 1. Packet Interceptor (WinDivert)
- **Purpose**: Captures outbound IPv4 packets and inbound IPv6 packets
- **Technology**: WinDivert kernel driver
- **Location**: `pkg/windivert/`

**Key Features:**
- Low-level packet capture at the network layer
- Minimal performance overhead
- Supports both IPv4 and IPv6 protocols
- Windows-specific implementation with cross-platform stubs

### 2. Address Translator
- **Purpose**: Maps IPv4 addresses to IPv6 destinations
- **Location**: `internal/translator/`

**Functionality:**
- Determines which IPv4 addresses should be intercepted
- Maintains bidirectional address mappings
- Supports subnet-based filtering
- Thread-safe operations

### 3. Connection Tracker
- **Purpose**: Maintains state for active connections
- **Location**: `internal/tracker/`

**Features:**
- Tracks TCP and UDP connections
- Connection lifecycle management
- Statistics collection
- Automatic cleanup of expired connections

### 4. Proxy Engine
- **Purpose**: Orchestrates packet processing and translation
- **Location**: `pkg/proxy/`

**Responsibilities:**
- Coordinates all components
- Handles packet processing pipelines
- Manages IPv4 ↔ IPv6 translation
- Provides statistics and monitoring

### 5. Configuration System
- **Purpose**: Manages application configuration
- **Location**: `pkg/config/`

**Supports:**
- YAML configuration files
- Command-line arguments
- Environment variables
- Runtime validation

### 6. Logging System
- **Purpose**: Provides structured logging
- **Location**: `pkg/logger/`

**Features:**
- Multiple log levels (debug, info, warn, error)
- Structured logging with fields
- Thread-safe operations
- Configurable output

## Data Flow

### Outbound Traffic (IPv4 → IPv6)

```
[Application] → [IPv4 Packet] → [WinDivert Capture] → [Address Translation] 
    ↓
[Connection Tracking] → [IPv6 Packet Creation] → [WinDivert Injection] → [Network]
```

1. **Packet Capture**: WinDivert intercepts outbound IPv4 packets
2. **Filtering**: Check if destination IP is in target subnet
3. **Translation**: Map IPv4 destination to IPv6 target
4. **Tracking**: Record connection information
5. **Conversion**: Create IPv6 packet with translated addresses
6. **Injection**: Send IPv6 packet to network

### Inbound Traffic (IPv6 → IPv4)

```
[Network] → [IPv6 Packet] → [WinDivert Capture] → [Address Translation]
    ↓
[Connection Lookup] → [IPv4 Packet Creation] → [WinDivert Injection] → [Application]
```

1. **Packet Capture**: WinDivert intercepts inbound IPv6 packets
2. **Translation**: Map IPv6 source back to original IPv4
3. **Lookup**: Find corresponding connection
4. **Conversion**: Create IPv4 packet with original addresses
5. **Injection**: Deliver IPv4 packet to application

## Threading Model

### Main Thread
- Configuration loading
- Component initialization
- Signal handling
- Graceful shutdown

### Outbound Processing Thread
- Captures outbound IPv4 packets
- Performs IPv4 → IPv6 translation
- Injects IPv6 packets

### Inbound Processing Thread
- Captures inbound IPv6 packets
- Performs IPv6 → IPv4 translation
- Injects IPv4 packets

### Cleanup Thread
- Periodic connection cleanup
- Statistics maintenance
- Memory management

## Memory Management

### Connection Tracking
- Fixed-size connection table
- LRU eviction for memory pressure
- Configurable connection limits
- Automatic timeout cleanup

### Packet Buffers
- Pre-allocated buffer pools
- Configurable buffer sizes
- Zero-copy operations where possible
- Efficient memory reuse

## Security Considerations

### Privilege Requirements
- **Administrator privileges required** for WinDivert driver
- Packet injection capabilities
- Network interface access

### Attack Surface
- Limited to configured subnet only
- No external network exposure
- Local system access only
- Driver-level security through WinDivert

### Data Protection
- No persistent storage of traffic
- In-memory processing only
- Connection state cleanup
- No logging of packet contents (by default)

## Performance Characteristics

### Throughput
- **Typical**: 100-500 Mbps depending on packet size
- **Factors**: CPU speed, memory bandwidth, packet rate
- **Optimization**: Buffer tuning, connection limits

### Latency
- **Added latency**: < 1ms typical
- **Factors**: Translation overhead, system load
- **Optimization**: Efficient packet processing

### Resource Usage
- **CPU**: Low to moderate (depends on traffic volume)
- **Memory**: Configurable (connection tracking overhead)
- **Network**: Minimal overhead (header translation only)

## Configuration Architecture

### Hierarchy (highest to lowest priority)
1. Command-line arguments
2. Environment variables
3. Configuration file
4. Default values

### Validation
- Schema validation for all inputs
- Network address validation
- Range checking for numeric values
- Early failure for invalid configurations

## Error Handling

### Graceful Degradation
- Continue operation on non-critical errors
- Log errors for debugging
- Maintain service availability

### Critical Failures
- Driver loading failures
- Network interface errors
- Memory allocation failures
- Immediate shutdown with error reporting

## Monitoring and Observability

### Metrics
- Packet counters (intercepted, forwarded, dropped)
- Connection statistics
- Error rates
- Performance metrics

### Logging
- Structured logging with context
- Configurable verbosity levels
- Performance-conscious logging
- Debug information for troubleshooting

## Platform Integration

### Windows-Specific Features
- WinDivert kernel driver integration
- Windows service compatibility
- Event log integration (future)
- Windows Firewall awareness

### Cross-Platform Considerations
- Stub implementations for non-Windows platforms
- Conditional compilation for platform-specific code
- Portable configuration and logging systems

## Future Enhancements

### Planned Features
1. **Multiple subnet support**: Handle multiple IPv4 subnets
2. **Load balancing**: Distribute traffic across multiple IPv6 targets
3. **Protocol-specific handling**: Enhanced TCP/UDP optimization
4. **Metrics endpoint**: Prometheus-compatible metrics
5. **Windows service**: Run as Windows service
6. **GUI interface**: Windows GUI for configuration

### Scalability Improvements
1. **Multi-threading**: Parallel packet processing
2. **NUMA awareness**: Optimize for multi-socket systems
3. **Hardware acceleration**: Leverage network card features
4. **Memory optimization**: Reduce per-connection overhead

## Dependencies

### Runtime Dependencies
- Windows 10/11 (64-bit)
- WinDivert driver (embedded)
- Administrator privileges

### Build Dependencies
- Go 1.19+
- gopkg.in/yaml.v3 (configuration)
- Standard library only

### Optional Dependencies
- Prometheus client (for metrics)
- Windows Event Log API (for logging)
- Windows Service API (for service mode)
