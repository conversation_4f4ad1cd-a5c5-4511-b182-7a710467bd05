# Usage Guide

## Quick Start

### Basic Usage

1. **Run with default settings:**
   ```cmd
   goproxy.exe
   ```
   This uses the default configuration:
   - Source subnet: `***********/24`
   - Target IPv6: `fd7a:115c:a1e0:b1a:0:7:c0a8:601`

2. **Run with custom settings:**
   ```cmd
   goproxy.exe -subnet ********/24 -target fd7a:115c:a1e0:b1a:0:7:c0a8:602
   ```

3. **Run with configuration file:**
   ```cmd
   goproxy.exe -config myconfig.yaml
   ```

### Command Line Options

```
Usage: goproxy.exe [options]

Options:
  -config string
        Path to configuration file
  -subnet string
        Source IPv4 subnet to intercept (default "***********/24")
  -target string
        Target IPv6 address (default "fd7a:115c:a1e0:b1a:0:7:c0a8:601")
  -log-level string
        Log level (debug, info, warn, error) (default "info")
  -interface string
        Network interface to bind to
  -max-connections int
        Maximum concurrent connections (default 1000)
  -timeout int
        Connection timeout in seconds (default 300)
  -buffer-size int
        Buffer size for packet processing (default 65536)
  -metrics
        Enable metrics endpoint (default false)
  -metrics-port int
        Metrics HTTP server port (default 8080)
```

## Configuration

### Configuration File

Create a `config.yaml` file:

```yaml
# Source subnet to intercept
source_subnet: "***********/24"

# Target IPv6 address
target_ipv6: "fd7a:115c:a1e0:b1a:0:7:c0a8:601"

# Logging level
log_level: "info"

# Network interface (optional)
bind_interface: ""

# Connection limits
max_connections: 1000
connection_timeout: 300

# Performance tuning
buffer_size: 65536

# Metrics (optional)
enable_metrics: false
metrics_port: 8080
```

### Environment Variables

You can also use environment variables:

```cmd
set GOPROXY_SOURCE_SUBNET=***********/24
set GOPROXY_TARGET_IPV6=fd7a:115c:a1e0:b1a:0:7:c0a8:601
set GOPROXY_LOG_LEVEL=debug
goproxy.exe
```

## Network Setup

### Tailscale/Headscale Scenario

1. **Device A (Windows with GoProxy):**
   - Connected to Tailscale network
   - Running GoProxy to access Device B's subnet

2. **Device B (Gateway with 4via6):**
   - Connected to Tailscale network
   - Has downstream devices on `***********/24`
   - Advertises IPv6 subnet via 4via6
   - IPv6 address: `fd7a:115c:a1e0:b1a:0:7:c0a8:601`

3. **Configuration:**
   ```yaml
   source_subnet: "***********/24"
   target_ipv6: "fd7a:115c:a1e0:b1a:0:7:c0a8:601"
   ```

### Testing the Setup

1. **Start the proxy:**
   ```cmd
   goproxy.exe -log-level debug
   ```

2. **Test connectivity:**
   ```cmd
   # Ping a device in the target subnet
   ping *************
   
   # Connect to a service
   telnet ************ 22
   
   # Browse to a web interface
   # Open browser and go to http://************
   ```

3. **Monitor logs:**
   Look for messages like:
   ```
   [2024-08-22 10:30:05.123] DEBUG: Translating outbound packet: ********:12345 -> *************:80 (IPv6: fd7a:115c:a1e0:b1a:0:7:c0a8:601)
   ```

## Advanced Usage

### Multiple Subnets

To handle multiple subnets, run multiple instances:

```cmd
# Terminal 1 - Handle ***********/24
goproxy.exe -subnet ***********/24 -target fd7a:115c:a1e0:b1a:0:7:c0a8:601

# Terminal 2 - Handle ********/24  
goproxy.exe -subnet ********/24 -target fd7a:115c:a1e0:b1a:0:7:c0a8:602
```

### Performance Tuning

For high-traffic scenarios:

```yaml
# Increase buffer size
buffer_size: 131072

# Increase connection limit
max_connections: 5000

# Reduce connection timeout for faster cleanup
connection_timeout: 60
```

### Monitoring

Enable metrics for monitoring:

```yaml
enable_metrics: true
metrics_port: 8080
```

Access metrics at: `http://localhost:8080/metrics`

## Troubleshooting

### Common Issues

1. **"Access denied" errors:**
   - Ensure running as Administrator
   - Check antivirus software

2. **No packets intercepted:**
   - Verify subnet configuration
   - Check routing table
   - Ensure target devices exist

3. **High CPU usage:**
   - Reduce buffer size
   - Increase connection timeout
   - Check for packet loops

### Debug Mode

Run with debug logging:

```cmd
goproxy.exe -log-level debug
```

This shows detailed packet processing information.

### Stopping the Proxy

- Press `Ctrl+C` to stop gracefully
- The proxy will display final statistics
- All connections will be properly cleaned up

## Best Practices

1. **Always run as Administrator**
2. **Use configuration files for complex setups**
3. **Monitor logs for errors**
4. **Test connectivity before production use**
5. **Keep the application updated**
6. **Document your network topology**

## Integration Examples

### PowerShell Script

```powershell
# Start GoProxy with custom configuration
$config = @"
source_subnet: "***********/24"
target_ipv6: "fd7a:115c:a1e0:b1a:0:7:c0a8:601"
log_level: "info"
"@

$config | Out-File -FilePath "proxy-config.yaml" -Encoding UTF8
Start-Process -FilePath "goproxy.exe" -ArgumentList "-config proxy-config.yaml" -Verb RunAs
```

### Batch Script

```batch
@echo off
echo Starting GoProxy...
goproxy.exe -subnet ***********/24 -target fd7a:115c:a1e0:b1a:0:7:c0a8:601 -log-level info
pause
```
