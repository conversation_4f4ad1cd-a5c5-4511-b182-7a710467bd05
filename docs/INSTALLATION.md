# Installation Guide

## Prerequisites

### System Requirements
- Windows 10/11 (64-bit)
- Administrator privileges
- Active network connection

### Dependencies
- WinDivert driver (automatically handled by the application)
- No additional software required

## Installation Methods

### Method 1: Download Pre-built Binary (Recommended)

1. Download the latest `goproxy.exe` from the releases page
2. Place the executable in a directory of your choice (e.g., `C:\Tools\goproxy\`)
3. Ensure you have Administrator privileges

### Method 2: Build from Source

#### Prerequisites for Building
- Go 1.19 or later
- Git (optional, for cloning the repository)

#### Build Steps

1. **Clone or download the source code:**
   ```bash
   git clone <repository-url>
   cd goproxy
   ```

2. **Build for Windows (on Windows):**
   ```cmd
   build.bat
   ```

3. **Cross-compile for Windows (from Linux/macOS):**
   ```bash
   ./build.sh
   ```

4. **Manual build:**
   ```bash
   go build -ldflags="-s -w" -o goproxy.exe ./cmd/goproxy
   ```

## WinDivert Driver

The application uses WinDivert for packet capture and injection. The driver is automatically loaded when the application starts and unloaded when it stops.

### Driver Installation
- No manual installation required
- The driver is embedded in the application
- Requires Administrator privileges to load

### Troubleshooting Driver Issues

If you encounter driver-related errors:

1. **Ensure Administrator privileges:**
   - Right-click on Command Prompt or PowerShell
   - Select "Run as administrator"
   - Navigate to the application directory
   - Run `goproxy.exe`

2. **Check Windows Defender/Antivirus:**
   - Some antivirus software may block the driver
   - Add an exception for `goproxy.exe`
   - Temporarily disable real-time protection for testing

3. **Verify system compatibility:**
   - Ensure you're running Windows 10/11
   - Check that your system is 64-bit
   - Verify no other packet capture software is running

## Verification

After installation, verify the application works:

1. **Check version:**
   ```cmd
   goproxy.exe -h
   ```

2. **Test configuration:**
   ```cmd
   goproxy.exe -subnet ***********/24 -target fd7a:115c:a1e0:b1a:0:7:c0a8:601 -log-level debug
   ```

3. **Expected output:**
   ```
   ╔═══════════════════════════════════════════════════════════════╗
   ║                          GoProxy v1.0.0                      ║
   ║                                                               ║
   ║           Transparent IPv4 to IPv6 Proxy for Windows         ║
   ║                                                               ║
   ║  Intercepts IPv4 traffic and forwards it to IPv6 addresses   ║
   ║  Perfect for Tailscale/Headscale networks with 4via6         ║
   ╚═══════════════════════════════════════════════════════════════╝

   [2024-08-22 10:30:00.000] INFO: Starting GoProxy v1.0.0
   [2024-08-22 10:30:00.001] INFO: Configuration loaded successfully
   [2024-08-22 10:30:00.002] INFO: Starting transparent proxy
   [2024-08-22 10:30:00.003] INFO: Transparent proxy started successfully
   [2024-08-22 10:30:00.004] INFO: Proxy is running. Press Ctrl+C to stop.
   ```

## Next Steps

After successful installation:

1. **Configure your network:** See [CONFIGURATION.md](CONFIGURATION.md)
2. **Set up your environment:** See [USAGE.md](USAGE.md)
3. **Test the proxy:** See [TESTING.md](TESTING.md)

## Uninstallation

To remove GoProxy:

1. Stop the running application (Ctrl+C)
2. Delete the `goproxy.exe` file
3. Remove any configuration files you created
4. No registry entries or system files are modified

## Support

If you encounter issues during installation:

1. Check the [Troubleshooting Guide](TROUBLESHOOTING.md)
2. Review the [FAQ](FAQ.md)
3. Open an issue on GitHub with:
   - Your Windows version
   - Error messages
   - Steps to reproduce the problem
