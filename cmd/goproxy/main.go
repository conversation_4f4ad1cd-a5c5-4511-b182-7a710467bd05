package main

import (
	"fmt"
	"os"
	"os/signal"
	"runtime"
	"syscall"

	"goproxy/pkg/config"
	"goproxy/pkg/logger"
	"goproxy/pkg/proxy"
)

const (
	appName    = "GoProxy"
	appVersion = "1.0.0"
)

func main() {
	// Print banner
	printBanner()

	// Check if running on Windows
	if runtime.GOOS != "windows" {
		fmt.Fprintf(os.Stderr, "Error: %s is only supported on Windows\n", appName)
		os.Exit(1)
	}

	// Load configuration
	cfg, err := config.LoadConfig()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error loading configuration: %v\n", err)
		os.Exit(1)
	}

	// Set up logging
	logger.SetDefaultLevelFromString(cfg.LogLevel)

	// Log startup information
	logger.Info("Starting %s v%s", appName, appVersion)
	logger.Info("Configuration loaded successfully")
	logger.Debug("Source subnet: %s", cfg.SourceSubnet)
	logger.Debug("Target IPv6: %s", cfg.TargetIPv6)
	logger.Debug("Log level: %s", cfg.LogLevel)

	// Create proxy instance
	p, err := proxy.NewProxy(cfg)
	if err != nil {
		logger.Fatal("Failed to create proxy: %v", err)
	}

	// Set up signal handling for graceful shutdown
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// Start the proxy
	if err := p.Start(); err != nil {
		logger.Fatal("Failed to start proxy: %v", err)
	}

	// Wait for shutdown signal
	logger.Info("Proxy is running. Press Ctrl+C to stop.")
	<-sigChan

	// Graceful shutdown
	logger.Info("Shutdown signal received, stopping proxy...")
	if err := p.Stop(); err != nil {
		logger.Error("Error stopping proxy: %v", err)
		os.Exit(1)
	}

	// Print final statistics
	stats := p.GetStats()
	logger.Info("Final statistics:")
	logger.Info("  Packets intercepted: %d", stats.PacketsIntercepted)
	logger.Info("  Packets forwarded: %d", stats.PacketsForwarded)
	logger.Info("  Packets dropped: %d", stats.PacketsDropped)
	logger.Info("  Bytes transferred: %d", stats.BytesTransferred)
	logger.Info("  Total connections: %d", stats.ConnectionsTotal)
	logger.Info("  Total errors: %d", stats.ErrorsTotal)

	logger.Info("Proxy stopped successfully")
}

func printBanner() {
	fmt.Printf(`
╔═══════════════════════════════════════════════════════════════╗
║                          %s v%s                           ║
║                                                               ║
║           Transparent IPv4 to IPv6 Proxy for Windows         ║
║                                                               ║
║  Intercepts IPv4 traffic and forwards it to IPv6 addresses   ║
║  Perfect for Tailscale/Headscale networks with 4via6         ║
╚═══════════════════════════════════════════════════════════════╝

`, appName, appVersion)
}
