# WinDivert API 修复总结

## 问题描述

项目在使用 WinDivert API 时遇到 "The parameter is incorrect" 错误，主要原因包括：

1. **WinDivert.dll 缺失**：项目目录中没有 WinDivert 库文件
2. **API 参数错误**：WinDivertOpen 函数的参数传递不正确
3. **结构体定义错误**：WINDIVERT_ADDRESS 结构体定义与官方 API 不匹配
4. **过滤器语法错误**：使用了不正确的过滤器字符串
5. **错误处理不完善**：缺乏详细的错误信息

## 修复内容

### 1. WinDivert 库安装修复

**问题**：缺少 WinDivert.dll 文件

**修复**：
- 创建了 `download-windivert.ps1` 自动下载脚本
- 更新了 `install-windivert-driver.bat` 安装脚本
- 添加了详细的安装指南 `WINDIVERT-SETUP.md`

**文件**：
- `download-windivert.ps1` - 自动下载 WinDivert 2.2.2
- `install-windivert-driver.bat` - 手动安装驱动
- `WINDIVERT-SETUP.md` - 详细安装指南

### 2. WinDivertOpen 参数修复

**问题**：参数类型和错误处理不正确

**修复**：
- 修复了 `priority` 参数的类型转换
- 改进了空字符串过滤器的处理
- 增强了错误信息的详细程度
- 添加了 Windows API 错误码获取

**文件**：`pkg/windivert/windivert.go`

**关键修改**：
```go
// 修复前
ret, _, errno := proc.Call(
    uintptr(unsafe.Pointer(filterPtr)),
    uintptr(layer),
    uintptr(priority),  // 可能有符号扩展问题
    uintptr(flags),
)

// 修复后
ret, _, errno := proc.Call(
    uintptr(unsafe.Pointer(filterPtr)),
    uintptr(layer),
    uintptr(int16(priority)), // 确保正确的符号扩展
    uintptr(flags),
)
```

### 3. WINDIVERT_ADDRESS 结构体修复

**问题**：结构体定义与官方 API 不匹配，使用了简单字段而不是位字段

**修复**：
- 重新设计结构体以匹配 WinDivert 2.2 官方定义
- 使用 `uint64` 存储位字段，通过位操作访问
- 添加了完整的访问器方法
- 同时更新了 Windows 和非 Windows 版本

**文件**：
- `pkg/windivert/windivert.go` (Windows 版本)
- `pkg/windivert/windivert_stub.go` (非 Windows 版本)

**关键修改**：
```go
// 修复前
type WinDivertAddress struct {
    Timestamp   int64
    Layer       uint8
    Event       uint8
    Sniffed     uint8
    Outbound    uint8
    // ... 其他简单字段
}

// 修复后
type WinDivertAddress struct {
    Timestamp int64  // INT64: timestamp when event occurred
    Flags     uint64 // 位字段打包到 uint64
    IfIdx     uint32 // UINT32: interface index
    SubIfIdx  uint32 // UINT32: sub-interface index
}

// 添加访问器方法
func (addr *WinDivertAddress) IsOutbound() bool {
    return (addr.Flags & WINDIVERT_ADDRESS_OUTBOUND_MASK) != 0
}

func (addr *WinDivertAddress) SetIPv6(ipv6 bool) {
    if ipv6 {
        addr.Flags |= WINDIVERT_ADDRESS_IPV6_MASK
    } else {
        addr.Flags &^= WINDIVERT_ADDRESS_IPV6_MASK
    }
}
```

### 4. 过滤器字符串优化

**问题**：使用了过于简单或不正确的过滤器

**修复**：
- 基于配置动态生成更精确的过滤器
- 添加了 IP 范围过滤器支持
- 改进了过滤器的优先级和回退机制
- 使用更符合 WinDivert 语法的过滤器

**文件**：`pkg/proxy/proxy.go`

**关键修改**：
```go
// 修复前
outboundFilters := []string{
    "false",
    "true", 
    "ip",
    "outbound",
}

// 修复后
outboundFilters := []string{
    // 最具体的过滤器 - 针对确切的子网
    fmt.Sprintf("outbound and ip.DstAddr >= %s and ip.DstAddr <= %s", 
        p.getSubnetStart(), p.getSubnetEnd()),
    
    // 回退过滤器，逐渐降低特异性
    fmt.Sprintf("outbound and ip.DstAddr >= %s", p.getSubnetStart()),
    "outbound and ip",
    "outbound",
    "ip",
    "true",
}
```

### 5. 代码兼容性修复

**问题**：代码使用了旧的结构体字段访问方式

**修复**：
- 更新所有使用 `addr.IPv6` 的地方为 `addr.SetIPv6()`
- 更新所有使用 `addr.Outbound` 的地方为 `addr.IsOutbound()`
- 确保 Windows 和非 Windows 版本的一致性

**文件**：
- `pkg/proxy/proxy.go`
- `cmd/fixed-test/main.go`

## 测试验证

### 创建的测试程序

1. **fixed-test.exe** - 综合测试程序
   - 测试 WinDivert 库加载
   - 测试基本过滤器
   - 测试网络过滤器
   - 测试组合过滤器
   - 测试实际包捕获

2. **param-test.exe** - 参数测试程序
   - 测试不同的参数组合
   - 验证错误处理

### 验证步骤

1. **下载 WinDivert**：
   ```powershell
   # 以管理员身份运行
   .\download-windivert.ps1
   ```

2. **测试修复**：
   ```cmd
   # 以管理员身份运行
   .\fixed-test.exe
   ```

3. **运行主程序**：
   ```cmd
   # 以管理员身份运行
   .\goproxy-fixed.exe
   ```

## 预期结果

修复后，程序应该能够：

1. ✅ 成功加载 WinDivert.dll
2. ✅ 正确打开 WinDivert 句柄
3. ✅ 使用正确的过滤器语法
4. ✅ 正确处理数据包地址信息
5. ✅ 提供详细的错误信息（如果仍有问题）

## 常见问题解决

### 如果仍然出现错误

1. **确保管理员权限**：必须以管理员身份运行
2. **检查防病毒软件**：可能需要添加例外
3. **验证 WinDivert 版本**：确保使用 WinDivert 2.2.2
4. **检查 Windows 版本**：确保兼容性
5. **启用测试签名**：某些系统可能需要启用测试签名模式

### 调试信息

运行程序时使用调试模式获取更多信息：
```cmd
.\goproxy-fixed.exe -log-level debug
```

## 技术细节

### WinDivert 2.2 关键变化

1. **结构体布局**：使用位字段而不是单独的字节字段
2. **参数验证**：更严格的参数验证
3. **错误代码**：更详细的错误信息
4. **过滤器语法**：支持更复杂的过滤器表达式

### 性能优化

1. **精确过滤器**：减少不必要的包捕获
2. **错误处理**：快速失败和回退机制
3. **内存管理**：正确的结构体对齐和大小

## 总结

通过这些修复，项目现在应该能够正确使用 WinDivert API，避免 "The parameter is incorrect" 错误。所有修改都遵循了 WinDivert 2.2 的官方文档和最佳实践。
