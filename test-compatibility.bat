@echo off
chcp 65001 >nul
title 兼容性测试

echo ========================================
echo Windows Server 2019 兼容性测试
echo ========================================
echo.

echo 1. 系统信息:
systeminfo | findstr /B /C:"OS Name" /C:"OS Version" /C:"System Type"
echo.

echo 2. 测试不同版本的程序:
echo.

echo 测试 simple-test.exe (64位):
if exist simple-test.exe (
    echo 尝试运行 simple-test.exe...
    simple-test.exe
    echo 退出代码: %ERRORLEVEL%
) else (
    echo ❌ simple-test.exe 不存在
)

echo.
echo 测试 simple-test-32bit.exe (32位):
if exist simple-test-32bit.exe (
    echo 尝试运行 simple-test-32bit.exe...
    simple-test-32bit.exe
    echo 退出代码: %ERRORLEVEL%
) else (
    echo ❌ simple-test-32bit.exe 不存在
)

echo.
echo 测试 minimal-test-compat.exe:
if exist minimal-test-compat.exe (
    echo 尝试运行 minimal-test-compat.exe...
    minimal-test-compat.exe
    echo 退出代码: %ERRORLEVEL%
) else (
    echo ❌ minimal-test-compat.exe 不存在
)

echo.
echo 3. 检查文件属性:
if exist simple-test.exe (
    echo simple-test.exe 属性:
    dir simple-test.exe
    echo 文件类型:
    file simple-test.exe 2>nul || echo "file 命令不可用"
)

echo.
echo 4. 检查依赖:
echo 检查 Visual C++ 运行时:
reg query "HKLM\SOFTWARE\Microsoft\VisualStudio\14.0\VC\Runtimes\x64" 2>nul && echo "VC++ 2015+ x64 已安装" || echo "VC++ 2015+ x64 未安装"
reg query "HKLM\SOFTWARE\Microsoft\VisualStudio\14.0\VC\Runtimes\x86" 2>nul && echo "VC++ 2015+ x86 已安装" || echo "VC++ 2015+ x86 未安装"

echo.
echo 测试完成。
pause
